import request from '@/utils/request'

/*//获取列表
export function getAccountList(query) {
    return request({
        url: '/caseManage/financial/account/list',
        method: 'get',
        params: query
    })
}

//添加账户
export function addAccount(data) {
    return request({
        url: '/caseManage/financial/account/insert',
        method: 'post',
        data: data
    })
}*/

// 获取投资人管理列表
export function gitInvestorList(query){
    return request({
        url: '/system/user/selectListByInvestor',
        method: 'get',
        params: query
    })
}

// 根据id获取资产包
export function getInvestorConfig(query) {
    return request({
        url: '/caseManage/investorConfig/info',
        method: 'get',
        params: query
    })
}

//获取资产包
export function gitManageList(query) {
    return request({
        url: '/caseManage/asset/manage/list',
        method: 'get',
        params: query
    })
}

// 绑定资产包
export function editInvestorConfig(data) {
    return request({
        url: '/caseManage/investorConfig/add',
        method: 'post',
        data: data
    })
}