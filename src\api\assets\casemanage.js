import request from '@/utils/request'

//分配状态
export function getAllocatedState() {
    return request({
        url: '/caseManage/case/start/getAllocatedState',
        method: 'get'
    })
}

//结清状态
export function getSettleState() {
    return request({
        url: '/caseManage/case/start/getSettleState',
        method: 'get'
    })
}

//案件状态
export function getCaseState() {
    return request({
        url: '/caseManage/case/start/getCaseState',
        method: 'get'
    })
}

//跟进状态
export function getFollowUpState() {
    return request({
        url: '/caseManage/case/start/getFollowUpState',
        method: 'get'
    })
}

//催收状态
export function getUrgeState() {
    return request({
        url: '/caseManage/case/start/getUrgeState',
        method: 'get'
    })
}

//批次下拉
export function getBatchNums() {
    return request({
        url: '/caseManage/case/start/getBatchNums',
        method: 'get'
    })
}

//获取委案批次号 
export function getEntrustingCaseBatchNums() {
    return request({
        url: '/caseManage/case/start/getEntrustingCaseBatchNums',
        method: 'get'
    })
}

//转让方下拉
export function getOwners() {
    return request({
        url: '/caseManage/case/start/getOwners',
        method: 'get'
    })
}

//户籍地下拉
export function getCensusRegisters() {
    return request({
        url: '/caseManage/case/start/getCensusRegisters',
        method: 'get'
    })
}

//标签下拉
export function getLabels() {
    return request({
        url: '/caseManage/case/start/getLabels',
        method: 'get'
    })
}

//案件管理库列表
export function caseManageList(query) {
    return request({
        url: '/caseManage/case/manage/list',
        method: 'get',
        params: query
    })
}

//案件操作搜索结果全选
export function queryAllid(query) {
    return request({
        url: '/caseManage/case/manage/queryAllId',
        method: 'get',
        params: query
    })
}
//案件操作搜索结果全选 -- 恢复案件
export function queryAllIdRestore(query) {
    return request({
        url: '/caseManage/case/manage/queryAllIdRestore',
        method: 'get',
        params: query
    })
}

//回收案件
export function reclaimCase(data) {
    return request({
        url: '/caseManage/case/manage/reclaimCase',
        method: 'post',
        data: data
    }) 
}

//退案
export function sendBackCase(data) {
    return request({
        url: '/caseManage/case/manage/sendBackCase',
        method: 'post',
        data: data
    }) 
}

//留案
export function stayCase(data) {
    return request({
        url: '/caseManage/case/manage/stayCase',
        method: 'post',
        data: data
    }) 
}

//停催案件
export function stopUrging(data) {
    return request({
        url: '/caseManage/case/manage/stopUrging',
        method: 'post',
        data: data
    }) 
}

//恢复案件
export function recoverCase(data) {
    return request({
        url: '/caseManage/case/manage/recoverCase',
        method: 'post',
        data: data
    }) 
}

//标签案件
export function setCaseLabel(data) {
    return request({
        url: '/caseManage/case/manage/setCaseLabel',
        method: 'post',
        data: data
    }) 
}

//案件统计
export function getCaseCount(query) {
    return request({
        url: '/caseManage/case/manage/caseCount',
        method: 'get',
        params: query
    })
}

//获取导出案件表头字段
export function getExportCaseHeaders() {
    return request({
        url: '/caseManage/case/manage/getExportCaseHeaders',
        method: 'get',
    })
}

//获取导出催记表头字段
export function getExportUrgeRecordHeaders() {
    return request({
        url: '/caseManage/case/manage/getExportUrgeRecordHeaders',
        method: 'get',
    })
}

//获取导出案件批次下拉
export function getCaseBatchNumOption() {
    return request({
        url: '/caseManage/case/manage/getCaseBatchNumOption',
        method: 'get',
    })
}


//发起工单
export function addworkorder(data) {
    return request({
        url: '/caseManage/case/workorder/add',
        method: 'post',
        data: data
    })
}

//渠道来源
export function getChannelSourceStates() {
    return request({
        url: '/caseManage/case/start/getChannelSourceStates',
        method: 'get'
    })
}

//问题类型
export function getQuestionTypeStates() {
    return request({
        url: '/caseManage/case/start/getQuestionTypeStates',
        method: 'get'
    })
}

//投诉等级
export function getComplaintLevelStates() {
    return request({
        url: '/caseManage/case/start/getComplaintLevelStates',
        method: 'get'
    })
}

//案件分配历史
export function getHistory(query) {
    return request({
        url: '/caseManage/case/history/list',
        method: 'get',
        params: query
    })
}

//预览分案
export function previewAllocationResults(data) {
    return request({
        url: '/caseManage/case/manage/previewAllocationResults',
        method: 'post',
        data: data
    })
}

//模板分案
export function templateAllocation(data) {
    return request({
        url: '/caseManage/case/manage/templateAllocation',
        method: 'post',
        data: data
    })
}

//查询异步进度
export function scheduleAllocation(query) {
    return request({
        url: '/caseManage/case/manage/scheduleAllocation',
        method: 'get',
        params: query
    })
}