import request from '@/utils/request'

//查询获取结清证明审批列表
export function settleList(query) {
    return request({
        url: '/caseManage/approve/settle/list',
        method: 'get',
        params: query
    })
}
//结清证明审批-通过
export function settlePass(data) {
    return request({
        url: '/caseManage/approve/settle/pass',
        method: 'post',
        data: data
    })
}
//结清证明审批-不通过
export function settleNotPass(data) {
    return request({
        url: '/caseManage/approve/settle/notPass',
        method: 'post',
        data: data
    })
}
//结清证明审批-处理流程
export function settleProce(query) {
    return request({
        url: '/caseManage/approve/settle/proce',
        method: 'get',
        params: query
    })
}
//结清证明审批-下发结清证明
export function settleRecordCertificate(data) {
    return request({
        url: '/caseManage/approve/settle/settleRecordCertificate',
        method: 'post',
        data: data
    })
}