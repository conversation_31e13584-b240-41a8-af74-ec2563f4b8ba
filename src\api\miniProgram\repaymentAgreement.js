import request from '@/utils/request'

// 查询还款协议列表
export function repaymentAgreementList(query) {
    return request({
        url: '/schedule/job/log/list',
        method: 'get',
        params: query
    })
}
// 模板类型-下拉
export function selectTemplateTypeOption(query) {
    return request({
        url: '/schedule/job/log/list',
        method: 'get',
        params: query
    })
}
// 模板名称-下拉
export function selectTemplateNameOption(query) {
    return request({
        url: '/schedule/job/log/list',
        method: 'get',
        params: query
    })
}
