import request from '@/utils/request'

//查询人行征信记录表
export function getCreditInvestigationList(query) {
    return request({
        url: '/hdcredit/sugar/rest/getList',
        method: 'get',
        params: query
    })
}
//查询页面-查询请求接口
export function getReqUrlList(query) {
    return request({
        url: '/hdcredit/sugar/rest/queryReqUrl',
        method: 'get',
        params: query
    })
}
//查看记录详情接口
export function getCreditInvestigationInfo(query) {
    return request({
        url: '/hdcredit/sugar/rest/getInfo',
        method: 'get',
        params: query
    })
}

//刷新
export function updateCreditRhzxBasic(data) {
    return request({
        url: 'hdcredit/creditRhzx/refresh',
        method: 'post',
        data: data
    })
}