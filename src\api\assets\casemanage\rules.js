import request from '@/utils/request'

//获取规则下拉
export function getStrategyOption() {
    return request({
        url: '/caseManage/case/strategy/getStrategyOption',
        method: 'get'
    })
}

//获取规则中文显示
export function getStrategyRulesChinese(id) {
    let query = {
        id: id
    }
    return request({
        url: '/caseManage/case/strategy/getStrategyRulesChinese',
        method: 'get',
        params: query
    })
}

//搜索结果
export function strategyAllocationQuery(query) {
    return request({
        url: '/caseManage/case/manage/strategyAllocationQuery',
        method: 'get',
        params: query
    })
}

//搜索结果
export function strategyAllocation(data) {
    return request({
        url: '/caseManage/case/manage/strategyAllocation',
        method: 'post',
        data: data
    })
}