import request from '@/utils/request'

//数据匹配
export function dataMatching(data) {
    return request({
        url: '/caseManage/case/manage/dataMatching',
        method: 'post',
        data: data
    })
}

//短信数据匹配
export function smsDataMatching(data) {
    return request({
        url: '/caseManage/case/manage/smsDataMatching',
        method: 'post',
        data: data,
        timeout: 5 * 60 * 1000
    })
}

//批量操作
export function batchOperation(data) {
    return request({
        url: '/caseManage/case/manage/batchOperation',
        method: 'post',
        data: data
    })
}

//批量操作
export function batchOperationBySms(data) {
    return request({
        url: '/caseManage/case/manage/batchOperationBySms',
        method: 'post',
        data: data,
        timeout: 5 * 60 * 1000
    })
}