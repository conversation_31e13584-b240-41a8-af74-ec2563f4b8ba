
import request from '@/utils/request'
//查询机构列表
export function organizationList(query) {
    return request({
        url: '/caseManage/assetsTeam/organizationList',
        method: 'get',
        params: query
    })
}

//根据条件查询总回款数据
export function lineChartProcessing(data) {
    return request({
        url: '/caseManage/assetsTeam/lineChartProcessing',
        method: 'post',
        data: data
    })
}

//根据条件查询总回款数据
export function lineChartProcessingTeam(data) {
    return request({
        url: '/caseManage/assetsTeam/lineChartProcessingTeam',
        method: 'post',
        data: data
    })
}

//查询机构列表
export function transferorQuery(query) {
    return request({
        url: '/caseManage/assetsTeam/transferorQuery',
        method: 'get',
        params: query
    })
}

//根据条件查询总回款数据
export function amountRangeSearch(data) {
    return request({
        url: '/caseManage/assetsTeam/amountRangeSearch',
        method: 'post',
        data: data
    })
}


//根据条件查询总回款数据
export function accountingPeriodInterval(data) {
    return request({
        url: '/caseManage/assetsTeam/accountingPeriodInterval',
        method: 'post',
        data: data
    })
}

//根据条件查询总回款数据
export function incomeAnalysis(query) {
    return request({
        url: '/caseManage/assetsTeam/incomeAnalysis',
        method: 'get',
        params: query
    })
}

//根据条件查询总回款数据
export function insertIncomeAnalysis(data) {
    return request({
        url: '/caseManage/assetsTeam/insertIncomeAnalysis',
        method: 'post',
        data: data
    })
}


//批次总回款数据-(图表)
export function batchProcessing(data) {
    return request({
        url: '/caseManage/assetsTeam/batchProcessing',
        method: 'post',
        data: data
    })
}

//批次总回款数据-（表格）
export function batchProcessingForm(data) {
    return request({
        url: '/caseManage/assetsTeam/batchProcessingForm',
        method: 'post',
        data: data
    })
}









