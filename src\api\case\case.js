import request from '@/utils/request'

//获取诉讼阶段下拉框
export function getStage(query) {
    return request({
        url: '/caseManage/lawsuit/case/getStage',
        method: 'get',
        params: query
    })
}

//获取受理法院
export function getLawOption(query) {
    return request({
        url: '/caseManage/lawsuit/case/getLaw',
        method: 'get',
        params: query
    })
}

//获取调诉机构诉讼案件查询
export function getLawList(query) {
    return request({
        url: '/caseManage/lawsuit/case/list',
        method: 'get',
        params: query
    })
}

//获取各个调诉阶段的案件数量
export function getStageCount(query) {
    return request({
        url: '/dispose/stage/register/getStageCount',
        method: 'get',
        params: query
    })
}
// 获取调诉统计数据
export function getStatisticsTotal(query) {
    return request({
        url: '/caseManage/lawsuit/case/getStatistics',
        method: 'get',
        params: query
    })
}