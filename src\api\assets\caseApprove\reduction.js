import request from '@/utils/request'

//获取减免审批列表
export function reductionList(query) {
    return request({
        url: '/caseManage/approve/reduction/list',
        method: 'get',
        params: query
    })
}

//通过
export function reductionPass(data) {
    return request({
        url: '/caseManage/approve/reduction/pass',
        method: 'post',
        data: data
    })
}

//不通过
export function reductionnotPass(data) {
    return request({
        url: '/caseManage/approve/reduction/notPass',
        method: 'post',
        data: data
    })
}

//查看案件进度
export function getProce(query) {
    return request({
        url: '/caseManage/approve/reduction/proce',
        method: 'get',
        params: query
    })
}

//减免审批-通过-(判断选中申请是否可以通过)
export function reductionDetermine(data) {
    return request({
        url: '/caseManage/approve/reduction/reductionDetermine',
        method: 'post',
        data: data
    })
}

//减免审批-查看凭证
export function selectStagingRecord(query) {
    return request({
        url: `/caseManage/approve/selectStagingRecord/${query.id}`,
        method: 'get',
        params: query
    })
}