import request from '@/utils/request'

//材料寄送统计 - 查询列表
export function getMaterialList(query) {
  return request({
    url: '/appeal/team/selectMaterialDeliveryList',
    method: 'get',
    params: query
  })
}

// 材料寄送统计 - 统计案件数量及金额
export function getMaterialMoney(data) {
  return request({
    url: '/appeal/team/selectMaterialDeliveryMoney',
    method: 'post',
    data: data
  })
}

// 获取快递信息
export function getExpressInfo(data) {
  return request({
    url: '/sign/letter/item/getExpressInfo',
    method: 'post',
    data: data
  })
}
//获取数据统计
export function selectMaterialDeliverySum(params) {
  return request({
      url: '/appeal/team/getSum',
      method: 'get',
      params
  })
}
//获取数据统计
export function caseExpressCaseCount(data) {
  return request({
      url: '/caseManage/litigation/caseExpressCaseCount',
      method: 'post',
      data
  })
}


