import request from '@/utils/request'

//查询授权记录
export function getCreditProtoco(query) {
    return request({
        url: '/hdcredit/creditProtoco/selectList',
        method: 'get',
        params: query
    })
}

//校验身份证
export function getCheckClientIdNum(query) {
    return request({
        url: '/hdcredit/creditRhzx/getCheck',
        method: 'get',
        params: query
    })
}

//获取人行征信字典选项常量
export function getDictRequired(query) {
    return request({
        url: '/hdcredit/creditRhzx/getRequired',
        method: 'get',
        params: query
    })
}

//上传协议
export function addCreditRhzx(data) {
    return request({
        url: '/hdcredit/creditRhzx/add',
        method: 'post',
        data: data
    })
}

//根据身份证号获取基本信息
export function getCreditRhzxBasic(query) {
    return request({
        url: '/hdcredit/creditRhzx/getBasic',
        method: 'get',
        params: query
    })
}

//开户报送/结清报送列表
export function getSubmittedCase(query) {
    return request({
        url: '/hdcredit/creditRhzx/getSubmittedCase',
        method: 'get',
        params: query
    })
}

//添加征信报送
export function addSubmittedsgmts(data) {
    return request({
        url: '/hdcredit/sugar/rest/sgmts',
        method: 'post',
        data: data
    })
}

//编辑基本信息
export function updateBasic(data) {
    return request({
        url: '/hdcredit/creditRhzx/updateBasic',
        method: 'post',
        data: data
    })
}

//获取省市区
export function getCityOptions(query) {
    return request({
        url: '/hdcredit/creditRhzx/getCityOptions',
        method: 'get',
        params: query
    })
}


//获取脱敏信息
export function getCreditRhzxBasicWriting(query) {
    return request({
        url: '/hdcredit/creditRhzx/getBasicS',
        method: 'get',
        params: query
    })
}

// 上传协议获取信息
export function caseInfoBase(query) {
    return request({
        url: '/hdcredit/creditRhzx/getCaseInfoBase',
        method: 'get',
        params: query
    })
}