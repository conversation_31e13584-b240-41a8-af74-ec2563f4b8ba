import request from '@/utils/request'

//获取列表 （根据批次号查询结算费率设置）
export function getBatchNum(query) {
    return request({
        url: '/caseManage/financial/rateSetting/selectBatchNum',
        method: 'get',
        params: query
    })
}

// 根据批次号查询结算费率设置记录
export function getRecordBatchNum(query) {
    return request({
        url: '/caseManage/financial/rateSetting/selectRecordBatchNum',
        method: 'get',
        params: query
    })
}

// 根据批次号查询对应案件数量
export function getCount(query) {
    return request({
        url: '/caseManage/financial/rateSetting/selectCount',
        method: 'get',
        params: query
    })
}

// 新增/修改结算费率设置--（批量）
export function insertRateSetting(data) {
    return request({
        url: '/caseManage/financial/rateSetting/insertRateSetting',
        method: 'post',
        data: data
    })
}
// ----------------------------------------------------- 奖项设置 -----------------------------------------------------------
// 新增奖项设置
export function insertAwardSetting(data) {
    return request({
        url: '/caseManage/financial/rateSetting/insertAwardSetting',
        method: 'post',
        data: data
    })
}

// 根据id修改奖项设置
export function updateAwardSetting(data) {
    return request({
        url: '/caseManage/financial/rateSetting/updateAwardSetting',
        method: 'post',
        data: data
    })
}

// 查询奖项设置
export function getAwardSetting(query) {
    return request({
        url: '/caseManage/financial/rateSetting/selectAwardSetting',
        method: 'get',
        params: query
    })
}

// 根据id查询奖项设置
export function getAwardSettingById(query) {
    return request({
        url: '/caseManage/financial/rateSetting/selectAwardSettingById',
        method: 'get',
        params: query
    })
}

// ----------------------------------------------------- 结佣日 -----------------------------------------------------------
// 根据团队id设置团队结佣日-(批量)
export function updateTeam(data) {
    return request({
        url: '/caseManage/financial/rateSetting/updateTeam',
        method: 'post',
        data
    })
}
// 根据团队id集合查询团队信息
export function getTeam(query) {
    return request({
        url: '/caseManage/financial/rateSetting/selectTeam',
        method: 'get',
        params: query
    })
}
// 根据团队id查询团队信息
export function getTeamById(query) {
    return request({
        url: '/caseManage/financial/rateSetting/selectTeamById',
        method: 'get',
        params: query
    })
}
// 根据团队id查询团队信息
export function getSettlementRecord(query) {
    return request({
        url: '/caseManage/financial/rateSetting/selectSettlementRecord',
        method: 'get',
        params: query
    })
}