import request from '@/utils/request'

// 调解案件导出
export function exportMediateData(data) {
  return request({
    url: '/caseManage/litigation/exportPhoneMediation',
    method: 'post',
    data: data,
    responseType: "blob"
  })
}

// 网上立案导出
export function exportFilingData(data) {
  return request({
    url: '/caseManage/litigation/exportFilingCase',
    method: 'post',
    data: data,
    responseType: "blob"
  })
}

// 立案开庭
export function exportCaseTrialData(data) {
  return request({
    url: '/caseManage/litigation/exportLawInfo',
    method: 'post',
    data: data,
    responseType: "blob"
  })
}

// 判决与结果
export function exportJudgmentData(data) {
  return request({
    url: '/caseManage/litigation/exportJudge',
    method: 'post',
    data: data,
    responseType: "blob"
  })
}

// 诉讼执行
export function exportLitigationData(data) {
  return request({
    url: '/caseManage/litigation/exportExecuteCase',
    method: 'post',
    data: data,
    responseType: "blob"
  })
}

// 诉讼保全
export function exportSaveData(data) {
  return request({
    url: '/caseManage/litigation/exportFreezeCase',
    method: 'post',
    data: data,
    responseType: "blob"
  })
}

// 材料寄出统计
export function exportMaterialData(data) {
  return request({
    url: '/appeal/team/exportWithMaterialDelivery',
    method: 'post',
    data: data,
    responseType: "blob"
  })
}