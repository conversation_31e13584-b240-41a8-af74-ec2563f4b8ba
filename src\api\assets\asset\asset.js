import request from '@/utils/request'

//获取列表
export function assetList(query) {
    return request({
        url: '/caseManage/asset/manage/list',
        method: 'get',
        params: query
    })
}

//获取列表统计数据
export function selectCount(query) {
    return request({
        url: '/caseManage/asset/manage/selectCount',
        method: 'get',
        params: query
    })
}

//导入案件
export function importCaseInfo(data) {
    return request({
        url: '/caseManage/asset/import/caseInfo',
        method: 'post',
        data: data
    })
}

//删除资产管理
export function deletedAset(id) {
    let data = {
        id: id
    }
    return request({
        url: '/caseManage/asset/manage/deleted',
        method: 'post',
        data: data
    })
}

//导入联系人(批次)
export function importContactBN(data) {
    return request({
        url: '/caseManage/asset/import/contactBatchNum',
        method: 'post',
        data: data
    })
}

//导入联系人(唯一案件ID匹配)
export function importContact(data) {
    return request({
        url: '/caseManage/asset/import/contact',
        method: 'post',
        data: data
    })
}

//导入还款计划(批量)
export function planBatch(data) {
    return request({
        url: '/caseManage/asset/import/planBatch',
        method: 'post',
        data: data
    })
}

//导入还款计划(批次)
export function planByAsset(data) {
    return request({
        url: '/caseManage/asset/import/planByAsset',
        method: 'post',
        data: data
    })
}

//导入催记(批量)
export function urgeRecordBatch(data) {
    return request({
        url: '/caseManage/asset/import/urgeRecordBatch',
        method: 'post',
        data: data
    })
}

//导入催记(批次)
export function urgeRecordByAsset(data) {
    return request({
        url: '/caseManage/asset/import/urgeRecordByAsset',
        method: 'post',
        data: data
    })
}

//减免设置
export function updateReduction(data) {
    return request({
        url: '/caseManage/asset/manage/updateReduction',
        method: 'post',
        data: data
    })
}

//债权总金额设置
export function updateEntrustMoney(data) {
    return request({
        url: '/caseManage/asset/manage/updateEntrustMoney',
        method: 'post',
        data: data
    })
}

//获取资产管理
export function getAssetDetail(id) {
    let query = {
        id: id
    }
    return request({
        url: '/caseManage/asset/manage/get',
        method: 'get',
        params: query
    })
}

//更新案件
export function updateCase(data) {
    return request({
        url: '/caseManage/asset/import/updateCase',
        method: 'post',
        data: data
    })
}

//重新导入案件
export function anewImportCaseInfo(data) {
    return request({
        url: '/caseManage/asset/import/anewImportCaseInfo',
        method: 'post',
        data: data
    })
}

//获取列表统计数据
export function getImportBatchNum(query) {
    return request({
        url: '/caseManage/asset/import/getImportBatchNum',
        method: 'get',
        params: query
    })
}

//获取减免中文信息
export function getReductionInfo(query) {
    return request({
        url: '/caseManage/setup/reduction/getReductionInfo',
        method: 'get',
        params: query
    })
}

// 编辑
export function editCaseInfo(data) {
    return request({
        url: '/caseManage/asset/manage/edit',
        method: 'post',
        data: data
    })
}

//资产包名称下拉选项
export function getPackName(query) {
    return request({
        url: '/caseManage/asset/import/selectPackageName',
        method: 'get',
        params: query
    })
}

//回款开户账号下拉选项
export function getRepayAccountList(query) {
    return request({
        url: '/caseManage/asset/import/selectOpenAccount',
        method: 'get',
        params: query
    })
}

//获取结清证明
export function getSettleList(query) {
    return request({
        url: '/sign/letter/template/getSettleList',
        method: 'get',
        params: query
    })
}

//获取冲减顺序
export function getOffsetOrderList(id) {
    return request({
        url: '/caseManage/asset/manage/getOffsetOrder',
        method: 'get',
        params: {id:id}
    })
}
//设置冲减顺序
export function updateOffsetOrder(data) {
    return request({
        url: '/caseManage/asset/manage/updateOffsetOrder',
        method: 'post',
        data: data
    })
}

//获取冲减费用选项
export function getOffsetOrderExpenseItem(query) {
    return request({
        url: '/caseManage/asset/manage/getOffsetOrderExpenseItem',
        method: 'get',
        params: query
    })
}
