import request from '@/utils/request'

//获取短信模板列表
export function selectCaseByAllowLawsuit(data) {
    return request({
      url: '/caseManage/caseManage/selectCaseByAllowLawsuit',
      method: 'post',
      data: data
    })
  }

//添加案件进度
export function insertSelective(data) {
  return request({
    url: '/caseManage/lawsuitStatus/insertSelective',
    method: 'post',
    data: data
  })
}

//查询诉讼进度
export function selectLawsuitCaseBySelective(data) {
  return request({
    url: '/caseManage/lawsuitStatus/selectLawsuitCaseBySelective',
    method: 'post',
    data: data
  })
}

//查询诉讼进度
export function selectDetailsByCaseId(data) {
  return request({
    url: '/caseManage/lawsuitStatus/selectDetailsByCaseId',
    method: 'post',
    data: data
  })
}

//导出诉讼记录
export function exportByCaseId(query) {
  return request({
    url: '/caseManage/lawsuitStatus/ExportByCaseId',
    method: 'get',
    params: query
  })
}

