import request from '@/utils/request'

//获取退案审批列表
export function backCaseList(query) {
    return request({
        url: '/caseManage/approve/sendBack/list',
        method: 'get',
        params: query
    })
}

//不通过
export function backCasenotPass(data) {
    return request({
        url: '/caseManage/approve/sendBack/notPass',
        method: 'post',
        data: data
    })
}

//通过
export function backCasePass(data) {
    return request({
        url: '/caseManage/approve/sendBack/pass',
        method: 'post',
        data: data
    })
}

//查看案件进度
export function getProce(query) {
    return request({
        url: '/caseManage/approve/sendBack/proce',
        method: 'get',
        params: query
    })
}

//退案审批 --通过-(判断选中申请是否可以通过)
export function sendBackDetermine(data) {
    return request({
        url: '/caseManage/approve/sendBack/sendBackDetermine',
        method: 'post',
        data: data
    })
}