import request from '@/utils/request'

//获取列表
export function getbelongList(query) {
  return request({
    url: '/caseManage/financial/bill/list',
    method: 'get',
    params: query
  })
}

//获取账单详情
export function getBillDetails(id) {
  return request({
    url: '/caseManage/financial/bill/get',
    method: 'get',
    params: {id: id}
  })
}

//编辑
export function editBill(data) {
  return request({
    url: '/caseManage/financial/bill/edit',
    method: 'post',
    data: data
  })
}

//删除
export function deleteBill(id) {
  return request({
    url: '/caseManage/financial/bill/deleted',
    method: 'post',
    data: {id: id}
  })
}
//批量删除
export function batchDeleteBill(data) {
  return request({
    url: '/caseManage/financial/bill/deletedS',
    method: 'post',
    data: data
  })
}

//解除关联
export function disassociateBill(id) {
  return request({
    url: '/caseManage/financial/bill/disassociate',
    method: 'post',
    data: {id: id}
  })
}