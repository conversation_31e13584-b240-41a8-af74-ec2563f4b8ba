import request from '@/utils/request'

//获取分期审批列表
export function stagingList(query) {
    return request({
        url: '/caseManage/approve/staging/list',
        method: 'get',
        params: query
    })
}

//审核通过
export function stagesPass(data) {
    return request({
        url: '/caseManage/approve/staging/pass',
        method: 'post',
        data: data
    })
}

//审核不通过
export function stagesnotPass(data) {
    return request({
        url: '/caseManage/approve/staging/notPass',
        method: 'post',
        data: data
    })
}

//查看案件进度
export function getProce(query) {
    return request({
        url: '/caseManage/approve/staging/proce',
        method: 'get',
        params: query
    })
}

//分期审批-通过-(判断选中申请是否可以通过)
export function stagingDetermine(data) {
    return request({
        url: '/caseManage/approve/staging/stagingDetermine',
        method: 'post',
        data: data
    })
}