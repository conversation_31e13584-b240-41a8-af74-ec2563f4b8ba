import request from '@/utils/request'

//获取案件联系人列表
export function contactList(query) {
  return request({
    url: '/caseManage/case/info/contact/list',
    method: 'get',
    params: query
  })
}

//案件联系人编辑
export function editContact(data) {
  return request({
    url: '/caseManage/case/info/contact/edit',
    method: 'post',
    data: data
  })
}

//获取案件详情
export function getCaseInfo(id) {
  let query = {
    caseId: id
  }
  return request({
    url: '/caseManage/case/library/getCaseDetails',
    method: 'get',
    params: query
  })
}

//催收记录
export function getUrge(query) {
  return request({
    url: '/caseManage/record/urge/list',
    method: 'get',
    params: query
  })
}

//回款记录
export function getRepay(query) {
  return request({
    url: '/caseManage/record/repayment/list',
    method: 'get',
    params: query
  })
}

//协催记录
export function getAssist(query) {
  return request({
    url: '/caseManage/record/assist/list',
    method: 'get',
    params: query
  })
}

//减免记录
export function getReduction(query) {
  return request({
    url: '/caseManage/record/reduction/list',
    method: 'get',
    params: query
  })
}

//诉讼记录
export function getLawsuit(query) {
  return request({
    url: '/caseManage/record/urge/registerRecordList',
    method: 'get',
    params: query
  })
}

//便签记录
export function getNote(query) {
  return request({
    url: '/caseManage/record/note/list',
    method: 'get',
    params: query
  })
}

//便签记录
export function getComplaint(query) {
  return request({
    url: '/caseManage/record/complaint/list',
    method: 'get',
    params: query
  })
}

//便签记录
export function getOutside(query) {
  return request({
    url: '/caseManage/record/outside/list',
    method: 'get',
    params: query
  })
}

// 通话记录
export function getCallLog(query) {
  return request({
    url: '/caseManage/record/callLog/list',
    method: 'get',
    params: query
  })
}

//短信记录
export function getNoteLog(query) {
  return request({
    url: '/caseManage/management/getSmsRecord',
    method: 'get',
    params: query
  })
}

//查询id查询回款信息
export function selectRepaymentPlan(query) {
  return request({
    url: '/caseManage/case/library/getRepaymentPlan',
    method: 'get',
    params: query
  })
}

//根据案件id查询还款计划汇总
export function selectRepaymentPlanSummary(caseId) {
  return request({
    url: `/caseManage/case/library/selectRepaymentPlanSummary/${caseId}`,
    method: 'get',
  })
}

//根据案件id查询案件共债信息列表
export function selectQueryJointDebt(query) {
  return request({
    url: '/caseManage/case/library/getJointDebt',
    method: 'get',
    params: query
  })
}

//根据案件id查询案件共债信息
export function selectQueryAmountMoney(query) {
  return request({
    url: '/caseManage/case/library/selectQueryAmountMoney',
    method: 'get',
    params: query
  })
}

//获取档案资料列表
export function getArchivalList(query) {
  return request({
    url: '/caseManage/management/retrieval/getArchivalList',
    method: 'get',
    params: query
  })
}

//获取文件在线预览主机地址
export function getFileOnlinePreviewHost(query) {
  return request({
    url: '/caseManage/management/getFileOnlinePreviewHost',
    method: 'get',
    params: query
  })
}

//根据案件id查询分期记录
export function getStagingList(query) {
  return request({
    url: '/caseManage/record/staging/list',
    method: 'get',
    params: query
  })
}
// 收费记录
export function caseCostRecordList(query) {
  return request({
    url: '/caseManage/record/urge/caseCostRecordList',
    method: 'get',
    params: query
  })
}

// 保全/调诉记录列表
export function registerRecordList(query) {
  return request({
    url: '/caseManage/record/urge/registerRecordList',
    method: 'get',
    params: query
  })
}
// 物流记录/调诉记录列表
export function expressRecordList(query) {
  return request({
    url: '/sign/letter/item/selectExpressInfo',
    method: 'get',
    params: query
  })
}
//获取调执记录
export function getPhoneUrgRecord(query) {
  return request({
    url: '/caseManage/record/urge/registerRecordList',
    method: 'get',
    params: query
  })
}
//获取委案记录详情
export function getCaseAllocationInfoApi(query) {
  return request({
    url: '/caseManage/case/library/getCaseAllocationInfo',
    method: 'get',
    params: query
  })
}
//阶段性进展-调诉类
export function getStageProgressTSApi(query) {
  return request({
    url: '/caseManage/case/library/stageProgressTs',
    method: 'get',
    params: query
  })
}
//阶段性进展-调执类
export function getStageProgressTzApi(query) {
  return request({
    url: '/caseManage/case/library/stageProgressTz',
    method: 'get',
    params: query
  })
}

//获取案件跟进+标签信息
export function getCaseFollowInfo(query) {
  return request({
    url: '/caseManage/case/library/getCaseFollowInfo',
    method: 'get',
    params: query
  })
}

//阶段性进展-保全
export function getStageProgressBqApi(query) {
  return request({
    url: '/caseManage/case/library/stageProgressBq',
    method: 'get',
    params: query
  })
}
