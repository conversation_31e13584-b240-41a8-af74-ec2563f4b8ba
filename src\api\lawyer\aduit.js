import request from '@/utils/request'

//律函-新建发函-校验文件
export function getMessageList(query) {
    return request({
        url: '/sign/letter/approve/messageList',
        method: 'get',
        params: query
    })
}

//律函审核-通过
export function messagePass(data) {
    return request({
        url: '/sign/letter/approve/messagePass',
        method: 'post',
        data: data
    })
}

//律函审核-不通过
export function messageNotPass(data) {
    return request({
        url: '/sign/letter/approve/messageNotPass',
        method: 'post',
        data: data
    })
}

//律函审核-函件量-列表
export function messageItemList(query) {
    return request({
        url: '/sign/letter/approve/itemList',
        method: 'get',
        params: query
    })
}

//律函审核-函件量-通过
export function messageItemPass(data) {
    return request({
        url: '/sign/letter/approve/itemPass',
        method: 'post',
        data: data
    })
}

//律函审核-函件量-不通过
export function messageItemNotPass(data) {
    return request({
        url: '/sign/letter/approve/itemNotPass',
        method: 'post',
        data: data
    })
}