import request from '@/utils/request'

//获取短信模板列表
export function getSelectTemplate(query) {
  return request({
    url: '/caseManage/management/selectTemplate',
    method: 'get',
    params: query
  })
}
//获取短信模板列表
export function selectAvailableTemplate(query) {
  return request({
    url: '/caseManage/management/selectAvailableTemplate',
    method: 'get',
    params: query
  })
}

//新增短信签名
export function addAutograph(data) {
  return request({
    url: '/caseManage/management/insertAutograph',
    method: 'post',
    data: data
  })
}

//修改短信签名
export function updateAutograph(data) {
  return request({
    url: '/caseManage/management/updateAutograph',
    method: 'post',
    data: data
  })
}

//删除短信签名
export function deleteAutograph(query) {
  return request({
    url: '/caseManage/management/deleteAutograph',
    method: 'post',
    params: query
  })
}

//删除短信模板
export function deleteTemplate(query) {
  return request({
    url: '/caseManage/management/deleteTemplate',
    method: 'post',
    params: query
  })
}

//新增短信模板
export function addTemplate(data) {
  return request({
    url: '/caseManage/management/insertTemplate',
    method: 'post',
    data: data
  })
}

//修改短信模板
export function updateTemplate(data) {
  return request({
    url: '/caseManage/management/updateTemplate',
    method: 'post',
    data: data
  })
}


//枚举
//获取模板参数枚举
export function getNoteTab(query) {
  return request({
    url: '/caseManage/management/select',
    method: 'get',
    params: query
  })
}

//获取短信签名列表
export function getSelectAutograph(query) {
  return request({
    url: '/caseManage/management/selectAutograph',
    method: 'get',
    params: query
  })
}

//获取短信通道
export function getSelectChannel(query) {
  return request({
    url: '/caseManage/management/selectChannel',
    method: 'get',
    params: query
  })
}

//获取短信预览
export function getPreview(data) {
  return request({
    url: '/caseManage/management/sendMessageVerification',
    method: 'post',
    data: data
  })
}

//获取短信预览(详情进入)
export function getPreviewDetails(data) {
  return request({
    url: '/caseManage/management/sendMessageByIdVerification',
    method: 'post',
    data: data
  })
}

//获取短信发送
export function getSendNote(data) {
  return request({
    url: '/caseManage/management/sendMessage',
    method: 'post',
    data: data
  })
}

//获取短信发送
export function getSendNoteDetails(data) {
  return request({
    url: '/caseManage/management/sendMessageById',
    method: 'post',
    data: data
  })
}

//修改短信模板
export function updateTemplateStatus(data) {
  return request({
    url: '/caseManage/management/updateTemplateStatus',
    method: 'post',
    data: data
  })
}

//根据条件查询案件信息，发送短信-(生成短信预览) --时效管理
export function getPreviewAging(data) {
  return request({
    url: '/caseManage/management/sendMessageVerificationWithTime',
    method: 'post',
    data: data
  })
}

//获取短信发送 -- 时效管理
export function getSendNoteAging(data) {
  return request({
    url: '/caseManage/management/sendMessageWithTime',
    method: 'post',
    data: data
  })
}
