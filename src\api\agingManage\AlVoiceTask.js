
import request from '@/utils/request'
//ai语音任务管理列表查询
export function aiTaskList(query) {
    return request({
        url: '/caseManage/aiVoiceTask/selectListTask',
        method: 'get',
        params: query
    })
}
// 查询任务结果
export function aiDetailsById(query) {
    return request({
        url: '/caseManage/aiVoiceTask/selectDetailTask',
        method: 'get',
        params: query
    })
}
// 根据任务id查询数据列表
export function taskDataById(query) {
    return request({
        url: '/caseManage/aiVoiceTask/selectRecordList',
        method: 'get',
        params: query
    })
}
// 新增ai语音任务
export function addAiTask(data) {
    return request({
        url: '/caseManage/aiVoiceTask/addTask',
        method: 'post',
        data: data
    })
}
// 编辑ai语音任务
export function updateAiTask(data) {
    return request({
        url: '/caseManage/aiVoiceTask/editStatus',
        method: 'put',
        data: data
    })
}