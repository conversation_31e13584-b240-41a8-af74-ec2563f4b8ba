import request from '@/utils/request'

//获取模板类型数据
export function getClassifyList(query) {
    return request({
        url: '/sign/letter/template/classify/getSelectTree',
        method: 'get',
        params: query
    })
}

//模板管理-列表
export function getTemplateList(query) {
    return request({
        url: '/sign/letter/template/list',
        method: 'get',
        params: query
    })
}

//选项-获取签章机构
export function getLawOption(query) {
    return request({
        url: '/sign/letter/template/sign',
        method: 'get',
        params: query
    })
}

//选项-获取代理律师
export function getAttorneyOption(query) {
    return request({
        url: '/option/getAttorneyOption',
        method: 'get',
        params: query
    })
}

//选项-获取创建用户
export function getUserOption(query) {
    return request({
        url: '/sys/user/getOption',
        method: 'get',
        params: query
    })
}

//判断模板名称是否唯一
export function checkUniqueName(query) {
    return request({
        url: '/sign/letter/template/checkUniqueName',
        method: 'get',
        params: query
    })
}

//判断模板名称是否唯一
export function getTemplateOptions(query) {
    return request({
        url: '/sign/letter/variable/getOptions',
        method: 'get',
        params: query
    })
}

//模板-获取全部信息(操作栏“编辑”时获取)
export function getDetails(query) {
    return request({
        url: '/sign/letter/template/getDetails',
        method: 'get',
        params: query
    })
}

//模板-获取预览(操作栏的‘详情’按钮)
export function getPreview(query) {
    return request({
        url: '/sign/letter/template/getPreview',
        method: 'get',
        params: query
    })
}

//模板-获取预览(操作栏的‘详情’按钮)
export function editStatus(data) {
    return request({
        url: '/sign/letter/template/editStatus',
        method: 'post',
        data: data
    })
}

//模板-预览
export function createTemplatePreview(data) {
    return request({
        url: '/sign/letter/template/createTemplatePreview',
        method: 'post',
        data: data
    })
}

//模板-创建
export function addTemplate(data) {
    return request({
        url: '/sign/letter/template/add',
        method: 'post',
        data: data
    })
}

//模板-编辑
export function editTemplate(data) {
    return request({
        url: '/sign/letter/template/edit',
        method: 'post',
        data: data
    })
}
// 模板-创建不盖章pdf文件
export function createTemplatePdf(data) {
    return request({
        url: '/sign/letter/template/createTemplatePdf',
        method: 'post',
        data: data
    })
}

//律所平台-获取当前律所名称
export function getLawName(query) {
    return request({
        url: '/option/getLawName',
        method: 'get',
        params: query
    })
}


// 签章-查询签章图片
export function getCompanySignature(query) {
    return request({
        url: '/option/getCompanySignature',
        method: 'get',
        params: query
    })
}

// 模板-创建模板(pdf压缩包导入模板)
export function creatTemplatePDFZip(data) {
    return request({
        url: '/sign/letter/template/creat',
        method: 'post',
        data: data
    })
}
// 模板-创建模板(pdf压缩包导入模板)
export function creatPreviewPDF(data) {
    return request({
        url: '/sign/letter/template/creatPreview',
        method: 'post',
        data: data
    })
}
// 模板-创建模板(pdf压缩包导入模板)
export function getPages(query) {
    return request({
        url: '/sign/letter/template/getPages',
        method: 'get',
        params: query
    })
}

// 删除模板
export function delTemplate(data) {
    return request({
        url: '/sign/letter/template/remove',
        method: 'post',
        data
    })
}
