import request from '@/utils/request'

//获取停催审批列表
export function allocatedList(query) {
    return request({
        url: '/caseManage/approve/allocated/list',
        method: 'get',
        params: query
    })
}

//获取审批状态下拉 
export function getExamineStates() {
    return request({
        url: '/caseManage/case/start/getExamineStates',
        method: 'get'
    })
}

//获取委案批次号 
export function getEntrustingCaseBatchNums() {
    return request({
        url: '/caseManage/case/start/getEntrustingCaseBatchNums',
        method: 'get'
    })
}

//不通过
export function allocatednotPass(data) {
    return request({
        url: '/caseManage/approve/allocated/notPass',
        method: 'post',
        data: data
    })
}

//通过
export function allocatedPass(data) {
    return request({
        url: '/caseManage/approve/allocated/pass',
        method: 'post',
        data: data
    })
}

//查看案件进度
export function getProce(query) {
    return request({
        url: '/caseManage/approve/allocated/proce',
        method: 'get',
        params: query
    })
}
//委案详情-列表
export function getGntrustingDetail(query) {
    return request({
        url: '/caseManage/approve/allocated/entrustingBatchNumDetail',
        method: 'get',
        params: query
    })
}
