import request from '@/utils/request'

//获取法院名称下拉框
export function getCourtNameOptions(query) {
  return request({
    url: '/caseManage/litigation/getCourtNameOptions',
    method: 'get',
    params: query
  })
}

// 根据调诉状态小阶段统计机构案件数量
export function getCaseSmallStageCount(data) {
  return request({
    url: '/caseManage/litigation/caseSmallStageCount',
    method: 'post',
    data: data
  })
}

// 诉讼保全排名
export function getCaseFreezeCaseCount(data) {
  return request({
    url: '/caseManage/litigation/caseFreezeCaseCount',
    method: 'post',
    data: data
  })
}

// 材料诉讼排序
export function getMaterialCount(query) {
  return request({
    url: '/appeal/team/materialDeliveryRanking',
    method: 'get',
    params: query
  })
}

// 调解排序
export function getMediateCaseCount(data) {
  return request({
    url: '/caseManage/litigation/mediateCaseCount',
    method: 'post',
    data: data
  })
}
