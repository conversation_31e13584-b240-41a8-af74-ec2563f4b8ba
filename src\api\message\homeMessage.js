import request from '@/utils/request'

//获取列表
export function messageList(query) {
  return request({
    url: '/message/asset/getList',
    method: 'get',
    params: query
  })
}

//删除消息
export function delMessage(data) {
  return request({
    url: '/message/asset/delete',
    method: 'post',
    data: data
  })
}

//删除消息
export function markRead(data) {
  return request({
    url: '/message/asset/markRead',
    method: 'post',
    data: data
  })
}

//获取消息内容
export function getUserNotice(query) {
  return request({
    url: '/message/asset/getUserNotice',
    method: 'get',
    params: query
  })
}

//获取导航栏消息
export function getNavMessage(query) {
  return request({
    url: '/message/asset/getNavMessage',
    method: 'get',
    params: query
  })
}