import request from '@/utils/request'

//获取留案审批列表
export function stayCaseList(query) {
    return request({
        url: '/caseManage/approve/stayCase/list',
        method: 'get',
        params: query
    })
}

//不通过
export function stayCasenotPass(data) {
    return request({
        url: '/caseManage/approve/stayCase/notPass',
        method: 'post',
        data: data
    })
}

//通过
export function stayCasePass(data) {
    return request({
        url: '/caseManage/approve/stayCase/pass',
        method: 'post',
        data: data
    })
}

//查看案件进度
export function getProce(query) {
    return request({
        url: '/caseManage/approve/stayCase/proce',
        method: 'get',
        params: query
    })
}

//查看案件进度
export function stayCaseDetermine(data) {
    return request({
        url: '/caseManage/approve/stayCase/stayCaseDetermine',
        method: 'post',
        data: data
    })
}
