import request from '@/utils/request'

// 查询用戶信息列表
export function userInfoList(query) {
    return request({
        url: '/caseManage/app-user-info/list',
        method: 'get',
        params: query
    })
}
// 获取在线客服详细信息
export function getOnlineInfo(query) {
    return request({
        url: '/caseManage/online/getInfo',
        method: 'get',
        params: query
    })
}
// 更新在线客服
export function updateOnline(query) {
    return request({
        url: '/caseManage/online/updateOnline',
        method: 'get',
        params: query
    })
}
