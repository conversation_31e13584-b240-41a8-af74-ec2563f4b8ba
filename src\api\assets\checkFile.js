import request from '@/utils/request'

//查询获取案件信息以及案件资料路径信息
export function validationExpired(query) {
    return request({
        url: '/caseManage/management/retrieval/validationExpired',
        method: 'get',
        params: query
    })
}

//根据申请id以及身份证后六位验证文件申请
export function certificateVerification(query) {
    return request({
        url: '/caseManage/management/retrieval/certificateVerification',
        method: 'get',
        params: query
    })
}

//根据案件id下载案件文件---(base64)
export function downloadFilesBase64(query) {
    return request({
        url: '/caseManage/management/retrieval/downloadFilesBase64',
        method: 'post',
        params: query
    })
}