import request from '@/utils/request'

//获取树
export function getImportState() {
    return request({
        url: '/caseManage/asset/importLog/getImportStart',
        method: 'get'
    })
}

//导入案件列表
export function imCaseList(query) {
    return request({
        url: '/caseManage/asset/importLog/caseList',
        method: 'get',
        params: query
    })
}

//导入联系人列表
export function imContactList(query) {
    return request({
        url: '/caseManage/asset/importLog/contactList',
        method: 'get',
        params: query
    })
}

//导入催记列表
export function imUrgeList(query) {
    return request({
        url: '/caseManage/asset/importLog/urgeList',
        method: 'get',
        params: query
    })
}

//导入还款计划
export function imPlanList(query) {
    return request({
        url: '/caseManage/asset/importLog/planList',
        method: 'get',
        params: query
    })
}

//导入批量操作
export function imBatchList(query) {
    return request({
        url: '/caseManage/asset/importLog/batchList',
        method: 'get',
        params: query
    })
}

// 导入资料
export function imDataList(query) {
    return request({
        url: '/caseManage/management/retrieval/selectRetrievalFileLog',
        method: 'get',
        params: query
    })
}
// 导入资料
export function getAllState(query) {
    return request({
        url: '/caseManage/management/retrieval/getAllState',
        method: 'get',
        params: query
    })
}