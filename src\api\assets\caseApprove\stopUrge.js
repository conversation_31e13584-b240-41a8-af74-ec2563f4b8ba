import request from '@/utils/request'

//获取停催审批列表
export function stopUrgeList(query) {
    return request({
        url: '/caseManage/approve/stopUrging/list',
        method: 'get',
        params: query
    })
}

//不通过
export function stopUrgenotPass(data) {
    return request({
        url: '/caseManage/approve/stopUrging/notPass',
        method: 'post',
        data: data
    })
}

//通过
export function stopUrgePass(data) {
    return request({
        url: '/caseManage/approve/stopUrging/pass',
        method: 'post',
        data: data
    })
}

//查看案件进度
export function getProce(query) {
    return request({
        url: '/caseManage/approve/stopUrging/proce',
        method: 'get',
        params: query
    })
}

//停催审批 --通过-(判断选中申请是否可以通过)
export function stopUrgingDetermine(data) {
    return request({
        url: '/caseManage/approve/stopUrging/stopUrgingDetermine',
        method: 'post',
        data: data
    })
}