<!-- 项目立项管理页面模板 -->
<template>
    <!-- 页面主容器，使用app-container样式类 -->
    <div class="app-container ">
        <!-- 搜索表单区域：内联表单布局，根据showSearch状态动态调整样式 -->
        <el-form inline label-width="auto" :class="!showSearch && 'form-h50'">
            <!-- 项目ID搜索输入框 -->
            <el-form-item label="项目ID" prop="">
                <el-input placeholder="请输入项目ID" style="width:320px" />
            </el-form-item>
            <!-- 产品类型搜索输入框 -->
            <el-form-item label="产品类型" prop="">
                <el-input placeholder="请输入产品类型" style="width:320px" />
            </el-form-item>
            <!-- 资产转让方搜索输入框 -->
            <el-form-item label="资产转让方" prop="">
                <el-input placeholder="请输入资产转让方" style="width:320px" />
            </el-form-item>
            <!-- 投标方式搜索输入框 -->
            <el-form-item label="投标方式" prop="">
                <el-input placeholder="请输入投标方式" style="width:320px" />
            </el-form-item>
            <!-- 债权总金额范围搜索：包含最小值和最大值两个输入框 -->
            <el-form-item label="债权总金额" prop="">
                <div class="range-area" style="width:320px">
                    <!-- 最小金额输入框 -->
                    <el-input />
                    <!-- 范围分隔符 -->
                    <span>-</span>
                    <!-- 最大金额输入框 -->
                    <el-input />
                </div>
            </el-form-item>
            <!-- 债权本金范围搜索：包含最小值和最大值两个输入框 -->
            <el-form-item label="债权本金" prop="">
                <div class="range-area" style="width:320px">
                    <!-- 最小本金输入框 -->
                    <el-input />
                    <!-- 范围分隔符 -->
                    <span>-</span>
                    <!-- 最大本金输入框 -->
                    <el-input />
                </div>
            </el-form-item>
            <!-- 立项人搜索输入框 -->
            <el-form-item label="立项人" prop="">
                <el-input placeholder="请输入立项人" style="width:320px" />
            </el-form-item>
            <!-- 立项时间搜索输入框 -->
            <el-form-item label="立项时间" prop="">
                <el-input placeholder="请输入立项时间" style="width:320px" />
            </el-form-item>
            <!-- 基准日搜索输入框 -->
            <el-form-item label="基准日" prop="">
                <el-input placeholder="请输入基准日" style="width:320px" />
            </el-form-item>
            <!-- 预计竞价日期搜索输入框 -->
            <el-form-item label="预计竞价日期" prop="">
                <el-input placeholder="请输入预计竞价日期" style="width:320px" />
            </el-form-item>
        </el-form>
        <!-- 搜索按钮区域：居中布局 -->
        <div class="text-center">
            <!-- 搜索按钮：主要按钮样式，带搜索图标，点击时执行防抖处理的查询函数 -->
            <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">搜索
            </el-button>
            <!-- 重置按钮：带刷新图标，点击时执行防抖处理的重置函数 -->
            <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
        </div>
        <!-- 操作按钮区域：包含主要的操作功能按钮 -->
        <div class="operation-revealing-area mb10">
            <!-- 新增立项按钮：主要按钮样式，点击跳转到新增页面 -->
            <el-button type="primary" @click="handleAdd()">新增立项</el-button>
            <!-- 提交立项按钮：朴素样式，点击打开申请对话框 -->
            <el-button plain @click="handleOpenDailog('ApplayRef')">提交立项</el-button>
            <!-- 删除按钮：朴素样式，点击删除选中项目 -->
            <el-button plain @click="handleDel(row)">删除</el-button>
            <!-- 右侧工具栏：包含搜索显示切换和列显示控制功能 -->
            <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="getList" />
        </div>
        <!-- 全选组件：用于批量选择和显示统计信息 -->
        <SelectedAll :dataList="dataList" v-model:selectedArr="selectedArr" v-model="queryParams.allQuery">
            <!-- 自定义内容插槽：显示项目统计信息 -->
            <template #content>
                <div class="ml20">
                    <!-- 项目数量统计显示，使用红色样式突出显示数字 -->
                    <span>项目量（件）：<i class="danger">222</i></span>
                </div>
            </template>
        </SelectedAll>
        <!-- 标签页组件：用于切换不同状态的项目列表 -->
        <el-tabs v-model="tabActive">
            <!-- 动态生成标签页，显示标签名称和对应的数量统计 -->
            <el-tab-pane v-for="v in tabList" :key="v" :label="`${v.label}(${v.count || 0})`" :name="v.value" />
        </el-tabs>
        <!-- 数据表格：显示项目立项信息列表 -->
        <el-table v-loading="loading" ref="multipleTableRef" :data="dataList" @selection-change="handleSelectionChange">
            <!-- 选择列：复选框列，支持自定义选择规则 -->
            <el-table-column type="selection" :selectable="selectable" width="30px" align="right" />
            <!-- 项目ID列：显示项目唯一标识，根据列配置控制显示/隐藏 -->
            <el-table-column label="项目ID" align="center" prop="projectId" min-width="160" v-if="columns[0].visible" />
            <!-- 项目名称列：显示项目的完整名称 -->
            <el-table-column label="项目名称" align="center" prop="projectName" min-width="180" v-if="columns[1].visible" />
            <!-- 产品类型列：显示项目的产品分类 -->
            <el-table-column label="产品类型" align="center" prop="produceType" min-width="120" v-if="columns[2].visible" />
            <!-- 立项状态列：显示项目当前的立项状态 -->
            <el-table-column label="立项状态" align="center" prop="fill" min-width="120" v-if="columns[3].visible" />
            <!-- 资产转让方列：显示资产的转让机构 -->
            <el-table-column label="资产转让方" align="center" prop="transfer" min-width="120" v-if="columns[4].visible" />
            <!-- 债权总金额列：显示债权的总金额，单位为元 -->
            <el-table-column label="债权总金额（元）" align="center" prop="entrustMoney" min-width="160"
                v-if="columns[5].visible" />
            <!-- 债权本金列：显示债权的本金金额，单位为元 -->
            <el-table-column label="债权本金（元）" align="center" prop="residualPrincipal" min-width="120"
                v-if="columns[6].visible" />
            <!-- 户数列：显示涉及的户数统计 -->
            <el-table-column label="户数" align="center" prop="houseNum" min-width="120" v-if="columns[7].visible" />
            <!-- 基准日列：显示项目的基准日期 -->
            <el-table-column label="基准日" align="center" prop="baseDate" min-width="120" v-if="columns[8].visible" />
            <!-- 预计竞价日期列：显示预计的竞价时间 -->
            <el-table-column label="预计竞价日期" align="center" prop="biddingDate" min-width="120"
                v-if="columns[9].visible" />
            <!-- 投标方式列：显示项目的投标方式 -->
            <el-table-column label="投标方式" align="center" prop="biddingMethod" min-width="120"
                v-if="columns[10].visible" />
            <!-- 报价上限列：显示项目的报价上限金额 -->
            <el-table-column label="报价上限" align="center" prop="quotationCeiling" min-width="120"
                v-if="columns[11].visible" />
            <!-- 资产评估表列：提供查看资产评估表的操作按钮 -->
            <el-table-column label="资产评估表" align="center" prop="" min-width="120" v-if="columns[12].visible">
                <template #default="{ row }">
                    <!-- 查看资产评估表按钮：文本样式，点击跳转到评估页面 -->
                    <el-button type="text" @click="handleCheck(row)">查看</el-button>
                </template>
            </el-table-column>
            <!-- 立项报告列：提供查看立项报告的操作按钮 -->
            <el-table-column label="立项报告" align="center" prop="" min-width="120" v-if="columns[13].visible">
                <template #default="{ row }">
                    <!-- 查看立项报告按钮：文本样式，点击打开文件预览对话框 -->
                    <el-button type="text" @click="handleOpenDailog('PerviewFileRef', row)">查看</el-button>
                </template>
            </el-table-column>
            <!-- 其他附件列：提供查看其他附件的操作按钮 -->
            <el-table-column label="其他附件" align="center" prop="" min-width="120" v-if="columns[14].visible">
                <template #default="{ row }">
                    <!-- 查看其他附件按钮：文本样式，点击打开文件预览对话框 -->
                    <el-button type="text" @click="handleOpenDailog('PerviewFileRef', row)">查看</el-button>
                </template>
            </el-table-column>
            <!-- 立项人列：显示项目的立项负责人 -->
            <el-table-column label="立项人" align="center" prop="projectProposer" min-width="120"
                v-if="columns[15].visible" />
            <!-- 立项时间列：显示项目的立项时间 -->
            <el-table-column label="立项时间" align="center" prop="projectDate" min-width="160"
                v-if="columns[16].visible" />
            <!-- 操作列：固定在右侧，包含各种操作按钮 -->
            <el-table-column fixed="right" width="180" label="操作">
                <template #default="{ row }">
                    <!-- 提交按钮：文本样式，点击打开申请对话框进行提交操作 -->
                    <el-button type="text" @click="handleOpenDailog('ApplayRef')">提交</el-button>
                    <!-- 详情按钮：文本样式，点击查看项目详细信息 -->
                    <el-button type="text" @click="handleDetails(row)">详情</el-button>
                    <!-- 删除按钮：文本样式，点击删除当前项目 -->
                    <el-button type="text" @click="handleDel(row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页组件：当总数大于0时显示，支持页码和每页条数的双向绑定 -->
        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="getList" />
        <!-- 申请对话框组件：用于处理立项提交申请 -->
        <Applay ref="ApplayRef" />
        <!-- 文件预览对话框组件：用于预览立项报告和其他附件 -->
        <PerviewFile ref="PerviewFileRef" />
    </div>
</template>

<!-- Vue 3 Composition API 脚本设置 -->
<script setup>
// 导入申请对话框组件
import Applay from '../../dueDiligence/startNapeList copy/dialog/applay.vue'
// 导入文件预览对话框组件
import PerviewFile from '../../dueDiligence/startNapeList copy/dialog/perviewFile.vue'

// 获取当前组件实例的代理对象，用于访问全局属性和方法
const { proxy } = getCurrentInstance()
// 获取当前路由信息
const route = useRoute()
// 获取路由器实例，用于页面跳转
const router = useRouter()
// 表格加载状态：控制表格的loading效果
const loading = ref(false)
// 项目数据列表：存储从后端获取的项目立项数据
const dataList = ref([
    // 项目1：信用贷类型，新增立项状态
    { projectId: 'zcb202411220001', projectName: '2024不良资产收购项目1', produceType: '信用贷', fill: '新增立项', transfer: '东莞银行', entrustMoney: '10,000.00', residualPrincipal: '9,000.00', houseNum: '1', baseDate: '2025-01-09', biddingDate: '2025-02-09', biddingMethod: '公开招标', quotationCeiling: '5,000.00', projectProposer: '胡图图', projectDate: '2024-11-10 12:00:00' },
    // 项目2：房贷类型，新增立项状态
    { projectId: 'zcb202411220002', projectName: '2024不良资产收购项目2', produceType: '房贷', fill: '新增立项', transfer: '东莞银行', entrustMoney: '10,000.00', residualPrincipal: '9,000.00', houseNum: '1', baseDate: '2025-01-09', biddingDate: '2025-02-09', biddingMethod: '公开招标', quotationCeiling: '5,000.00', projectProposer: '胡图图', projectDate: '2024-11-10 12:00:00' },
    // 项目3：房贷类型，已提交状态
    { projectId: 'zcb202411220003', projectName: '2024不良资产收购项目3', produceType: '房贷', fill: '已提交', transfer: '东莞银行', entrustMoney: '10,000.00', residualPrincipal: '9,000.00', houseNum: '1', baseDate: '2025-01-09', biddingDate: '2025-02-09', biddingMethod: '公开招标', quotationCeiling: '5,000.00', projectProposer: '胡图图', projectDate: '2024-11-10 12:00:00' },
    // 项目4：房贷类型，已提交状态
    { projectId: 'zcb202411220004', projectName: '2024不良资产收购项目4', produceType: '房贷', fill: '已提交', transfer: '东莞银行', entrustMoney: '10,000.00', residualPrincipal: '9,000.00', houseNum: '1', baseDate: '2025-01-09', biddingDate: '2025-02-09', biddingMethod: '公开招标', quotationCeiling: '5,000.00', projectProposer: '胡图图', projectDate: '2024-11-10 12:00:00' },
])
// 选中项数组：存储用户在表格中选中的项目数据
const selectedArr = ref([])
// 搜索区域显示状态：控制搜索表单的显示/隐藏
const showSearch = ref(false)
// 查询参数对象：包含分页和查询条件
const queryParams = ref({
    pageNum: 1,        // 当前页码
    pageSize: 10,      // 每页显示条数
    allQuery: false    // 是否查询全部数据
})
// 数据总条数：用于分页组件显示总数
const total = ref(4)
// 当前激活的标签页索引：默认显示第一个标签页（新增立项）
const tabActive = ref(0)
// 标签页列表配置：定义不同状态的项目分类
const tabList = ref([
    { label: '新增立项', count: 88, value: 0 },   // 新增立项状态的项目，数量88个
    { label: '已提交', count: 88, value: 1 },     // 已提交状态的项目，数量88个
    { label: '全部', count: 176, value: 2 },      // 全部项目，总数176个
])
// 表格列配置：控制各列的显示/隐藏状态
const columns = ref([
    { "key": 0, "label": "项目ID", "visible": true },           // 项目唯一标识列
    { "key": 1, "label": "项目名称", "visible": true },         // 项目名称列
    { "key": 2, "label": "产品类型", "visible": true },         // 产品分类列
    { "key": 3, "label": "立项状态", "visible": true },         // 立项状态列
    { "key": 4, "label": "资产转让方", "visible": true },       // 资产转让机构列
    { "key": 5, "label": "债权总金额（元）", "visible": true }, // 债权总金额列
    { "key": 6, "label": "债权本金（元）", "visible": true },   // 债权本金列
    { "key": 7, "label": "户数", "visible": true },             // 涉及户数列
    { "key": 8, "label": "基准日", "visible": true },           // 基准日期列
    { "key": 9, "label": "预计竞价日期", "visible": true },     // 预计竞价日期列
    { "key": 10, "label": "投标方式", "visible": true },        // 投标方式列
    { "key": 11, "label": "报价上限", "visible": true },        // 报价上限列
    { "key": 12, "label": "资产评估表", "visible": true },      // 资产评估表操作列
    { "key": 13, "label": "立项报告", "visible": true },        // 立项报告操作列
    { "key": 14, "label": "其他附件", "visible": true },        // 其他附件操作列
    { "key": 15, "label": "立项人", "visible": true },          // 立项负责人列
    { "key": 16, "label": "立项时间", "visible": true }         // 立项时间列
])

/**
 * 获取项目列表数据
 * 功能：从后端API获取项目立项数据并更新dataList
 * 注意：当前为空函数，需要实现具体的数据获取逻辑
 */
function getList() {
    // TODO: 实现数据获取逻辑
    // 1. 根据queryParams构建请求参数
    // 2. 调用API接口获取数据
    // 3. 更新dataList和total
    // 4. 处理loading状态
}

/**
 * 删除项目处理函数
 * @param {Object} row - 要删除的项目行数据
 * 功能：显示确认对话框并处理项目删除操作
 */
function handleDel(row) {
    // 定义删除确认提示信息
    const tipMsg = '确认是否删除？'
    // 显示确认对话框，用户确认后执行删除操作
    proxy.$modal.confirm(tipMsg)
}

/**
 * 打开对话框处理函数
 * @param {string} refName - 组件引用名称，用于标识要打开的对话框组件
 *
 * 功能说明：
 * 1. 接收一个引用名称参数，该参数对应模板中定义的ref属性
 * 2. 通过Vue实例的$refs属性访问对应的子组件实例
 * 3. 调用子组件的openDialog方法来显示对话框
 *
 * 使用场景：
 * - refName为'ApplayRef'时：打开申请相关的对话框组件
 * - refName为'PerviewFileRef'时：打开文件预览对话框组件
 */
function handleOpenDailog(refName) {
    // 通过Vue实例代理对象访问$refs，获取指定名称的组件引用
    // 然后调用该组件实例的openDialog方法来打开对话框
    proxy.$refs[refName].openDialog();
}

/**
 * 新增项目处理函数
 * @param {Object} row - 项目行数据（可选）
 * 功能：跳转到项目信息页面进行新增操作
 */
function handleAdd(row) {
    // 构建路由查询参数，包含当前路径、页面类型、进度状态和详情标识
    const query = { path: route.path, pageType: 'startNapeList', progressStatus: 0, isDetails: 0 }
    // 跳转到尽职调查项目信息页面
    router.push({ path: `/dueDiligence/projectInfo`, query })
}

/**
 * 表格行选择规则函数
 * @returns {boolean} 返回是否可选择该行
 * 功能：根据查询参数决定表格行是否可以被选择
 */
function selectable() {
    // 当不是查询全部数据时，行才可以被选择
    return !queryParams.value.allQuery
}

/**
 * 查看项目详情处理函数
 * @param {Object} row - 项目行数据
 * 功能：跳转到项目详情页面查看项目信息
 */
function handleDetails(row) {
    // 构建路由查询参数，包含当前路径、页面类型和进度状态
    const query = { path: route.path, pageType: 'startNapeList', progressStatus: 0 }
    // 跳转到尽职调查项目信息页面
    router.push({ path: `/dueDiligence/projectInfo`, query })
}

/**
 * 查看资产评估表处理函数
 * @param {Object} row - 项目行数据
 * 功能：跳转到资产评估查看页面
 */
function handleCheck(row) {
    // 跳转到评估查看操作页面，传递当前路径作为查询参数
    router.push({
        path: `/assessment/view-evaluation/operate/1`,
        query: { path: route.path },
    });
}
</script>

<!-- 组件样式：使用SCSS预处理器，scoped确保样式只作用于当前组件 -->
<style lang="scss" scoped>
/* 当前组件暂无自定义样式，使用全局样式和Element Plus默认样式 */
/* 可在此处添加组件特定的样式定制 */
</style>