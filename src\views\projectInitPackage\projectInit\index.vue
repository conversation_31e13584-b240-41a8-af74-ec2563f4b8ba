<template>
    <div class="app-container ">
        <el-form inline label-width="auto" :class="!showSearch && 'form-h50'">
            <el-form-item label="项目ID" prop="">
                <el-input placeholder="请输入项目ID" style="width:320px" />
            </el-form-item>
            <el-form-item label="产品类型" prop="">
                <el-input placeholder="请输入产品类型" style="width:320px" />
            </el-form-item>
            <el-form-item label="资产转让方" prop="">
                <el-input placeholder="请输入资产转让方" style="width:320px" />
            </el-form-item>
            <el-form-item label="投标方式" prop="">
                <el-input placeholder="请输入投标方式" style="width:320px" />
            </el-form-item>
            <el-form-item label="债权总金额" prop="">
                <div class="range-area" style="width:320px">
                    <el-input />
                    <span>-</span>
                    <el-input />
                </div>
            </el-form-item>
            <el-form-item label="债权本金" prop="">
                <div class="range-area" style="width:320px">
                    <el-input />
                    <span>-</span>
                    <el-input />
                </div>
            </el-form-item>
            <el-form-item label="立项人" prop="">
                <el-input placeholder="请输入立项人" style="width:320px" />
            </el-form-item>
            <el-form-item label="立项时间" prop="">
                <el-input placeholder="请输入立项时间" style="width:320px" />
            </el-form-item>
            <el-form-item label="基准日" prop="">
                <el-input placeholder="请输入基准日" style="width:320px" />
            </el-form-item>
            <el-form-item label="预计竞价日期" prop="">
                <el-input placeholder="请输入预计竞价日期" style="width:320px" />
            </el-form-item>
        </el-form>
        <div class="text-center">
            <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">搜索
            </el-button>
            <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
        </div>
        <div class="operation-revealing-area mb10">
            <el-button type="primary" @click="handleAdd()">新增立项</el-button>
            <el-button plain @click="handleOpenDailog('ApplayRef')">提交立项</el-button>
            <el-button plain @click="handleDel(row)">删除</el-button>
            <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="getList" />
        </div>
        <SelectedAll :dataList="dataList" v-model:selectedArr="selectedArr" v-model="queryParams.allQuery">
            <template #content>
                <div class="ml20">
                    <span>项目量（件）：<i class="danger">222</i></span>
                </div>
            </template>
        </SelectedAll>
        <el-tabs v-model="tabActive">
            <el-tab-pane v-for="v in tabList" :key="v" :label="`${v.label}(${v.count || 0})`" :name="v.value" />
        </el-tabs>
        <el-table v-loading="loading" ref="multipleTableRef" :data="dataList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" :selectable="selectable" width="30px" align="right" />
            <el-table-column label="项目ID" align="center" prop="projectId" min-width="160" v-if="columns[0].visible" />
            <el-table-column label="项目名称" align="center" prop="projectName" min-width="180" v-if="columns[1].visible" />
            <el-table-column label="产品类型" align="center" prop="produceType" min-width="120" v-if="columns[2].visible" />
            <el-table-column label="立项状态" align="center" prop="fill" min-width="120" v-if="columns[3].visible" />
            <el-table-column label="资产转让方" align="center" prop="transfer" min-width="120" v-if="columns[4].visible" />
            <el-table-column label="债权总金额（元）" align="center" prop="entrustMoney" min-width="160"
                v-if="columns[5].visible" />
            <el-table-column label="债权本金（元）" align="center" prop="residualPrincipal" min-width="120"
                v-if="columns[6].visible" />
            <el-table-column label="户数" align="center" prop="houseNum" min-width="120" v-if="columns[7].visible" />
            <el-table-column label="基准日" align="center" prop="baseDate" min-width="120" v-if="columns[8].visible" />
            <el-table-column label="预计竞价日期" align="center" prop="biddingDate" min-width="120"
                v-if="columns[9].visible" />
            <el-table-column label="投标方式" align="center" prop="biddingMethod" min-width="120"
                v-if="columns[10].visible" />
            <el-table-column label="报价上限" align="center" prop="quotationCeiling" min-width="120"
                v-if="columns[11].visible" />
            <el-table-column label="资产评估表" align="center" prop="" min-width="120" v-if="columns[12].visible">
                <template #default="{ row }">
                    <el-button type="text" @click="handleCheck(row)">查看</el-button>
                </template>
            </el-table-column>
            <el-table-column label="立项报告" align="center" prop="" min-width="120" v-if="columns[13].visible">
                <template #default="{ row }">
                    <el-button type="text" @click="handleOpenDailog('PerviewFileRef', row)">查看</el-button>
                </template>
            </el-table-column>
            <el-table-column label="其他附件" align="center" prop="" min-width="120" v-if="columns[14].visible">
                <template #default="{ row }">
                    <el-button type="text" @click="handleOpenDailog('PerviewFileRef', row)">查看</el-button>
                </template>
            </el-table-column>
            <el-table-column label="立项人" align="center" prop="projectProposer" min-width="120"
                v-if="columns[15].visible" />
            <el-table-column label="立项时间" align="center" prop="projectDate" min-width="160"
                v-if="columns[16].visible" />
            <el-table-column fixed="right" width="180" label="操作">
                <template #default="{ row }">
                    <el-button type="text" @click="handleOpenDailog('ApplayRef')">提交</el-button>
                    <el-button type="text" @click="handleDetails(row)">详情</el-button>
                    <el-button type="text" @click="handleDel(row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="getList" />
        <Applay ref="ApplayRef" />
        <PerviewFile ref="PerviewFileRef" />
    </div>
</template>

<script setup>
import Applay from '../../dueDiligence/startNapeList copy/dialog/applay.vue'
import PerviewFile from '../../dueDiligence/startNapeList copy/dialog/perviewFile.vue'

const { proxy } = getCurrentInstance()
const route = useRoute()
const router = useRouter()
const loading = ref(false)
const dataList = ref([
    { projectId: 'zcb202411220001', projectName: '2024不良资产收购项目1', produceType: '信用贷', fill: '新增立项', transfer: '东莞银行', entrustMoney: '10,000.00', residualPrincipal: '9,000.00', houseNum: '1', baseDate: '2025-01-09', biddingDate: '2025-02-09', biddingMethod: '公开招标', quotationCeiling: '5,000.00', projectProposer: '胡图图', projectDate: '2024-11-10 12:00:00' },
    { projectId: 'zcb202411220002', projectName: '2024不良资产收购项目2', produceType: '房贷', fill: '新增立项', transfer: '东莞银行', entrustMoney: '10,000.00', residualPrincipal: '9,000.00', houseNum: '1', baseDate: '2025-01-09', biddingDate: '2025-02-09', biddingMethod: '公开招标', quotationCeiling: '5,000.00', projectProposer: '胡图图', projectDate: '2024-11-10 12:00:00' },
    { projectId: 'zcb202411220003', projectName: '2024不良资产收购项目3', produceType: '房贷', fill: '已提交', transfer: '东莞银行', entrustMoney: '10,000.00', residualPrincipal: '9,000.00', houseNum: '1', baseDate: '2025-01-09', biddingDate: '2025-02-09', biddingMethod: '公开招标', quotationCeiling: '5,000.00', projectProposer: '胡图图', projectDate: '2024-11-10 12:00:00' },
    { projectId: 'zcb202411220004', projectName: '2024不良资产收购项目4', produceType: '房贷', fill: '已提交', transfer: '东莞银行', entrustMoney: '10,000.00', residualPrincipal: '9,000.00', houseNum: '1', baseDate: '2025-01-09', biddingDate: '2025-02-09', biddingMethod: '公开招标', quotationCeiling: '5,000.00', projectProposer: '胡图图', projectDate: '2024-11-10 12:00:00' },
])
const selectedArr = ref([])
const showSearch = ref(false)
const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
    allQuery: false
})
const total = ref(4)
const tabActive = ref(0)
const tabList = ref([
    { label: '新增立项', count: 88, value: 0 },
    { label: '已提交', count: 88, value: 1 },
    { label: '全部', count: 176, value: 2 },
])
const columns = ref([
    { "key": 0, "label": "项目ID", "visible": true },
    { "key": 1, "label": "项目名称", "visible": true },
    { "key": 2, "label": "产品类型", "visible": true },
    { "key": 3, "label": "立项状态", "visible": true },
    { "key": 4, "label": "资产转让方", "visible": true },
    { "key": 5, "label": "债权总金额（元）", "visible": true },
    { "key": 6, "label": "债权本金（元）", "visible": true },
    { "key": 7, "label": "户数", "visible": true },
    { "key": 8, "label": "基准日", "visible": true },
    { "key": 9, "label": "预计竞价日期", "visible": true },
    { "key": 10, "label": "投标方式", "visible": true },
    { "key": 11, "label": "报价上限", "visible": true },
    { "key": 12, "label": "资产评估表", "visible": true },
    { "key": 13, "label": "立项报告", "visible": true },
    { "key": 14, "label": "其他附件", "visible": true },
    { "key": 15, "label": "立项人", "visible": true },
    { "key": 16, "label": "立项时间", "visible": true }
])

function getList() {

}
// 删除
function handleDel(row) {
    const tipMsg = '确认是否删除？'
    proxy.$modal.confirm(tipMsg)
}
function handleOpenDailog(refName) {
    proxy.$refs[refName].openDialog();
}
function handleAdd(row) {
    const query = { path: route.path, pageType: 'startNapeList', progressStatus: 0, isDetails: 0 }
    router.push({ path: `/dueDiligence/projectInfo`, query })
}
function selectable() {
    return !queryParams.value.allQuery
}
function handleDetails(row) {
  const query = { path: route.path, pageType: 'startNapeList', progressStatus: 0 }
  router.push({ path: `/dueDiligence/projectInfo`, query })
}
function handleCheck(row) {
    router.push({
        path: `/assessment/view-evaluation/operate/1`,
        query: { path: route.path },
    });
}
</script>

<style lang="scss" scoped></style>