<template>
  <div class="app-container">
    <el-form inline label-width="100px" :class="{ 'form-h50': !showSearch }" ref="queryRef">
      <el-form-item prop="caseId" label="资产名称">
        <el-input style="width: 320px" v-model="queryParams.caseId" placeholder="请输入资产名称" />
      </el-form-item>
      <el-form-item prop="caseId" label="项目名称">
        <el-input style="width: 320px" v-model="queryParams.caseId" placeholder="请输入项目名称" />
      </el-form-item>
      <el-form-item prop="caseId" label="立项人">
        <el-input style="width: 320px" v-model="queryParams.caseId" placeholder="请输入立项人" />
      </el-form-item>
      <el-form-item prop="caseId" label="立项时间">
        <el-input style="width: 320px" v-model="queryParams.caseId" placeholder="请输入立项时间" />
      </el-form-item>
      <el-form-item prop="caseId" label="审核状态">
        <el-select v-model="queryParams.caseId" style="width: 320px" placeholder="请选择审核状态">
          <el-option />
        </el-select>
      </el-form-item>
    </el-form>
    <div class="text-center">
      <el-button @click="antiShake(resetQuery)">重置</el-button>
      <el-button type="primary" @click="antiShake(handleQuery)">搜索</el-button>
    </div>
    <div class="operation-revealing-area mb20">
      <el-button type="primary" @click="handleOpenDailog('applayRef')">提交立项aaa</el-button>
      <el-button type="primary" @click="handleAdd()">新增立项</el-button>
      <el-button type="primary" @click="handleOpenDailog('applayRef')">删除</el-button>
      <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="getList" />
    </div>
    <el-tabs v-model="activeName" @tab-change="antiShake(resetQuery)">
      <el-tab-pane v-for="(item, index) in erectNapeEnum" :key="index" :label="item" :name="index" />
    </el-tabs>
    <div class="table-box">
      <el-table :data="dataList" :loading="loading">
        <el-table-column label="项目ID" v-if="columns[0].visible" width="160" align="center" prop="projectId" />
        <el-table-column label="项目名称" v-if="columns[1].visible" width="200" align="center" prop="projectName" />
        <el-table-column label="产品类型" v-if="columns[2].visible" width="120" align="center" prop="type" />
        <el-table-column label="立项状态" v-if="columns[3].visible" width="120" align="center" prop="status" />
        <el-table-column label="资产转让方" v-if="columns[4].visible" width="120" align="center" prop="transferor" />
        <el-table-column label="债权总金额" v-if="columns[5].visible" width="120" align="center" prop="debtTotalAmount" />
        <el-table-column label="债权本金" v-if="columns[6].visible" width="120" align="center" prop="principalAmount" />
        <el-table-column label="户数" v-if="columns[7].visible" width="120" align="center" prop="debtCount" />
        <el-table-column label="基准日" v-if="columns[8].visible" width="120" align="center" prop="baselineDate" />
        <el-table-column label="预计竞价日期" v-if="columns[9].visible" width="120" align="center"
          prop="expectedAuctionDate" />
        <el-table-column label="报价上限" v-if="columns[10].visible" width="120" align="center" prop="priceLimit" />
        <el-table-column label="资产评估表" v-if="columns[11].visible" width="120" align="center">
          <template #default="{ row }">
            <div>
              <el-button type="text" @click="handleCheck(row)">查看</el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="立项报告" v-if="columns[12].visible" width="120" align="center">
          <template #default="{ row }">
            <div>
              <el-button type="text" @click="handleOpenDailog('perviewFileRef', row)">查看</el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="其他附件" v-if="columns[13].visible" width="120" align="center" prop="surface" />
        <el-table-column label="立项人" v-if="columns[14].visible" width="120" align="center" prop="createBy" />
        <el-table-column label="立项时间" v-if="columns[15].visible" width="160" align="center" prop="createTime" />
        <el-table-column fixed="right" width="220" label="操作">
          <template #default="{ row }">
            <div>
              <el-button type="text" @click="handleSubmit(row)">提交</el-button>
              <el-button type="text" @click="handleDetails(row)">详情</el-button>
              <el-button type="text" @click="handleDel(row)">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </div>
    <applay ref="applayRef" />
    <perviewFile ref="perviewFileRef" />
  </div>
</template>

<script setup name="StartNapeList">
import perviewFile from '../startNapeList copy/dialog/perviewFile';
import { erectNapeEnum } from "@/utils/enum";
const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();
const queryParams = ref({
  pageNum: 1, pageSize: 10
});
const total = ref(1);
const dataList = ref([
  {
    projectId: 'zcb202411220001',
    projectName: '2024不良资产收购项目1',
    type: '信用贷',
    status: '新增立项',
    transferor: '东莞银行',
    debtTotalAmount: '10,000.00',
    principalAmount: '9,000.00',
    debtCount: '1',
    baselineDate: '2025-01-09',
    expectedAuctionDate: '2025-02-09',
    priceLimit: '公开招标',
    surface: '5,000.00',
    createBy: '胡图图',
    createTime: '2024-11-10 12:00:00',
  },
  {
    projectId: 'zcb202411220002',
    projectName: '2024不良资产收购项目2',
    type: '房贷',
    status: '新增立项',
    transferor: '东莞银行',
    debtTotalAmount: '10,000.00',
    principalAmount: '9,000.00',
    debtCount: '1',
    baselineDate: '2025-01-09',
    expectedAuctionDate: '2025-02-09',
    priceLimit: '公开招标',
    surface: '5,000.00',
    createBy: '胡图图',
    createTime: '2024-11-10 12:00:00',
  },
  {
    projectId: 'zcb202411220003',
    projectName: '2024不良资产收购项目3',
    type: '房贷',
    status: '已提交',
    transferor: '东莞银行',
    debtTotalAmount: '10,000.00',
    principalAmount: '9,000.00',
    debtCount: '1',
    baselineDate: '2025-01-09',
    expectedAuctionDate: '2025-02-09',
    priceLimit: '公开招标',
    surface: '5,000.00',
    createBy: '胡图图',
    createTime: '2024-11-10 12:00:00',
  },
  {
    projectId: 'zcb202411220004',
    projectName: '2024不良资产收购项目4',
    type: '房贷',
    status: '已提交',
    transferor: '东莞银行',
    debtTotalAmount: '10,000.00',
    principalAmount: '9,000.00',
    debtCount: '1',
    baselineDate: '2025-01-09',
    expectedAuctionDate: '2025-02-09',
    priceLimit: '公开招标',
    surface: '5,000.00',
    createBy: '胡图图',
    createTime: '2024-11-10 12:00:00',
  },
]);
const loading = ref(false);
const showSearch = ref(false);
const columns = ref([
  { "key": 0, "label": "项目ID", "visible": true },
  { "key": 1, "label": "项目名称", "visible": true },
  { "key": 2, "label": "产品类型", "visible": true },
  { "key": 3, "label": "立项状态", "visible": true },
  { "key": 4, "label": "资产转让方", "visible": true },
  { "key": 5, "label": "债权总金额", "visible": true },
  { "key": 6, "label": "债权本金", "visible": true },
  { "key": 7, "label": "户数", "visible": true },
  { "key": 8, "label": "基准日", "visible": true },
  { "key": 9, "label": "预计竞价日期", "visible": true },
  { "key": 10, "label": "报价上限", "visible": true },
  { "key": 11, "label": "资产评估表", "visible": true },
  { "key": 12, "label": "立项报告", "visible": true },
  { "key": 13, "label": "其他附件", "visible": true },
  { "key": 14, "label": "立项人", "visible": true },
  { "key": 15, "label": "立项时间", "visible": true }
]);
function getList() { }
function resetQuery() {
  queryParams.value = { pageNum: 1, pageSize: 10 };
  getList();
}
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

function handleOpenDailog(refName) {
  // proxy.$refs[refName].openDialog();
}
function handleDetails(row) {
  const query = { path: route.path, pageType: 'startNapeList', progressStatus: 0 }
  router.push({ path: `/dueDiligence/projectInfo`, query })
}
function handleAdd(row) {
  const query = { path: route.path, pageType: 'startNapeList', progressStatus: 0, isDetails: 0 }
  router.push({ path: `/dueDiligence/projectInfo`, query })
}
function handleCheck(row) {
  router.push({
    path: `/assessment/view-evaluation/operate/1`,
    query: { path: route.path },
  });
}
</script>

<style lang="scss" scoped></style>