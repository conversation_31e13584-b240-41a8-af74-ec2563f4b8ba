import request from '@/utils/request'

//获取回款审批列表
export function repaymentList(query) {
    return request({
        url: '/caseManage/approve/repayment/list',
        method: 'get',
        params: query
    })
}

//获取回款金额
export function repaymentListCount(query) {
    return request({
        url: '/caseManage/approve/repayment/listCount',
        method: 'get',
        params: query
    })
}

//回款审核不通过类型 
export function getNotPassType() {
    return request({
        url: '/caseManage/approve/repayment/getNotPassType',
        method: 'get'
    })
}

//不通过
export function repaymentNotPass(data) {
    return request({
        url: '/caseManage/approve/repayment/notPass',
        method: 'post',
        data: data
    })
}

//下发结清证明
export function settleCertificate(data) {
    return request({
        url: '/caseManage/approve/repayment/settleCertificate',
        method: 'post',
        data: data
    })
}

//获取还款渠道
export function getRepaymentOptions() {
    return request({
        url: '/caseManage/setup/repayment/getRepaymentOptions',
        method: 'get'
    })
}

//获取还款渠道必填字段
export function getRegisterPayment(id) {
    return request({
        url: '/caseManage/setup/repayment/getRegisterPayment',
        method: 'get',
        params: {id: id}
    })
}

//获取回款记录信息
export function getRepaymentInfo(id) {
    return request({
        url: '/caseManage/approve/repayment/getInfo',
        method: 'get',
        params: {id: id}
    })
}

//关联未归属 
export function relevanceNotBelong(data) {
    return request({
        url: '/caseManage/approve/repayment/relevanceNotBelong',
        method: 'post',
        data: data,
        timeout:3*60*1000
    })
}

//查看案件进度
export function getProce(query) {
    return request({
        url: '/caseManage/approve/relevance/proce',
        method: 'get',
        params: query
    })
}

//回款审批
export function relevanceDetermine(query) {
    return request({
        url: '/caseManage/approve/repayment/relevanceDetermine',
        method: 'get',
        params: query
    })
}

//解除关联
export function disassociate(id) {
    return request({
        url: '/caseManage/approve/disassociate',
        method: 'post',
        data: {id: id}
    })
}
