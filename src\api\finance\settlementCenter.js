import request from '@/utils/request'

//导入案件
export function selectTeamBrokerage(query) {
    return request({
      url: '/caseManage/financial/teamBrokerage/selectTeamBrokerage',
      method: 'get',
      params: query
    })
  }

//获取状态枚举
export function getStatus(query) {
  return request({
    url: '/caseManage/financial/teamBrokerage/getStatus',
    method: 'get',
    params: query
  })
}

//查询资产回收情况
export function getAssetRecovery(query) {
  return request({
    url: '/caseManage/financial/teamBrokerage/getAssetRecovery',
    method: 'get',
    params: query
  })
}

//查询风险奖罚设置
export function getTeamRisk(query) {
  return request({
    url: '/caseManage/financial/teamBrokerage/getTeamRisk',
    method: 'get',
    params: query
  })
}

//获取总计数据
export function getTotal(query) {
  return request({
    url: '/caseManage/financial/teamBrokerage/getTotal',
    method: 'get',
    params: query
  })
}

//删除资产回收数据
export function removeAssetRecovery(query) {
  return request({
    url: '/caseManage/financial/teamBrokerage/removeAssetRecovery/' + query.id,
    method: 'DELETE',
  })
}

//删除机构风险奖罚设置数据
export function removeTeamRisk(query) {
  return request({
    url: '/caseManage/financial/teamBrokerage/removeTeamRisk/' + query.id,
    method: 'DELETE',
  })
}

//获取总计数据
export function getAwardData(query) {
  return request({
    url: '/caseManage/financial/teamBrokerage/getAwardData',
    method: 'get',
    params: query
  })
}

//获取佣金详情表
export function getDetails(query) {
  return request({
    url: '/caseManage/financial/teamBrokerage/getDetails',
    method: 'get',
    params: query
  })
}


//风险奖罚_添加
export function addTeamRisk(data) {
  return request({
    url: '/caseManage/financial/teamBrokerage/addTeamRisk',
    method: 'post',
    data: data
  })
}

//确认并发送佣金确认函
export function confirmSending(query) {
  return request({
    url: '/caseManage/financial/teamBrokerage/confirmSending',
    method: 'post',
    params: query
  })
}

//同步回款记录
export function synchronizeRecords(query) {
  return request({
    url: '/caseManage/financial/teamBrokerage/synchronizeRecords',
    method: 'post',
    params: query
  })
}

//确认结佣提交凭证信息
export function insertVoucherRecord(data) {
  return request({
    url: '/caseManage/financial/teamBrokerage/insertVoucherRecord',
    method: 'post',
    data: data
  })
}

//根据条件查询结佣凭证记录信息
export function selectVoucherRecord(query) {
  return request({
    url: '/caseManage/financial/teamBrokerage/selectVoucherRecord',
    method: 'get',
    params: query
  })
}



