import request from '@/utils/request'

//导入案件
export function importCollection(data) {
    return request({
      url: '/caseManage/collectionFile/importCollection',
      method: 'post',
      data: data
    })
  }

//导入还款记录（导入记录查询）分页
export function selectCollectionFile(query) {
    return request({
      url: '/caseManage/collectionFile/selectCollectionFile',
      method: 'get',
      params: query
    })
  }

//导入还款记录数据查询--分页
export function selectCollectionHistorical(query) {
    return request({
      url: '/caseManage/collectionFile/selectCollectionHistorical',
      method: 'get',
      params: query
    })
  }