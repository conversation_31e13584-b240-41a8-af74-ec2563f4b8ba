import request from '@/utils/request'

//质检模型--列表
export function selectTaskListApi(query) {
    return request({
        url: '/quality/model/list',
        method: 'get',
        params: query
    })
}

//质检模型--删除
export function removeTaskApi(data) {
    return request({
        url: '/quality/model/remove',
        method: 'post',
        data: data
    })
}
//质检模型--添加
export function addTaskApi(data) {
    return request({
        url: '/quality/model/add',
        method: 'post',
        data: data
    })
}
//质检模型--修改
export function editTaskApi(data) {
    return request({
        url: '/quality/model/edit',
        method: 'post',
        data: data
    })
}
//录音质检--获取质检详情
export function getInfoTaskApi(data) {
    return request({
        url: '/quality/quality/getInfo',
        method: 'post',
        data: data
    })
}
//录音质检--获取质检对话文本
export function getTextTaskApi(data) {
    return request({
        url: '/quality/quality/getText',
        method: 'post',
        data: data
    })
}
//录音质检--复检
export function artificialTaskApi(data) {
    return request({
        url: '/quality/quality/artificial',
        method: 'post',
        data: data
    })
}
