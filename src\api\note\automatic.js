import request from '@/utils/request'

//获取发送记录列表
export function selectSendTask(query) {
    return request({
      url: '/caseManage/sms/task/list',
      method: 'get',
      params: query
    })
  }

//新增短信发送设置
export function addSend(data) {
    return request({
      url: '/caseManage/sms/task/add',
      method: 'post',
      data: data
    })
  }

//修改短信发送设置
export function editSend(data) {
    return request({
      url: '/caseManage/sms/task/edit',
      method: 'post',
      data: data
    })
  }


//修改短信发送设置
export function editSendStatus(data) {
  return request({
    url: '/caseManage/sms/task/editState',
    method: 'post',
    data: data
  })
}
  

//删除短信发送设置
export function delSend(data) {
    return request({
      url: '/caseManage/sms/task/deleted',
      method: 'post',
      data: data
    })
  }

//自动短信-获取短信模板
export function getTemplateOption(query) {
    return request({
      url: '/caseManage/sms/task/getTemplateOption',
      method: 'get',
      params: query
    })
  }

