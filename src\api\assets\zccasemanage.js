import request from '@/utils/request'

//获取树
export function getTree() {
    return request({
        url: '/caseManage/asset/signature/getTree',
        method: 'get'
    })
}

//获取树
export function getTreeMenu() {
    return request({
        url: '/caseManage/asset/signature/getTreeMenu',
        method: 'get'
    })
}

//获取列表
export function getlibrarylist(query) {
    return request({
        url: '/caseManage/case/library/list',
        method: 'get',
        params: query
    })
}

//分配到案件池
export function assignToCaseManage(data) {
    return request({
        url: '/caseManage/case/library/assignToCaseManage',
        method: 'post',
        data: data
    })
}

//案件统计
export function getCaseCount(query) {
    return request({
        url: '/caseManage/case/library/selectCount',
        method: 'get',
        params: query
    })
}

//获取产品下拉
export function getProductOptions() {
    return request({
        url: '/caseManage/case/start/getProductOption',
        method: 'get'
    })
}

//获取产品下拉
export function getExportHeaders() {
    return request({
        url: '/caseManage/case/library/getExportHeaders',
        method: 'get'
    })
}

//征信报送
export function creditReportPort(data) {
    return request({
        url: '/caseManage/credit/reporting',
        method: 'post',
        data: data
    })
}