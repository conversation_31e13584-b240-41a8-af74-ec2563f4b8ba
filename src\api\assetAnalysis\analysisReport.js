import request from '@/utils/request'
// 查询转让方+资产包
export function queryManageAndBatch() {
    return request({
        url: '/caseManage/evaluation/queryManageAndBatch',
        method: 'get',

    })
}
// 查询模型
export function queryModel(data) {
    return request({
        url: '/caseManage/evaluation/queryModel',
        method: 'post',
        data
    })
}
// 资产概述
export function overviewAsset(data) {
    return request({
        url: '/caseManage/evaluation/overviewAsset',
        method: 'post',
        data
    })
}
