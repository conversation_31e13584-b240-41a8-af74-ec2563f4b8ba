import request from '@/utils/request'

//获取转让方-产品树 
export function assetOwnerTree() {
    return request({
        url: '/caseManage/asset/owner/getTree',
        method: 'get'
    })
}

//添加转让方
export function addAssetOwner(data) {
    return request({
        url: '/caseManage/asset/owner/add',
        method: 'post',
        data: data
    })
}

//编辑转让方
export function editAssetOwner(data) {
    return request({
        url: '/caseManage/asset/owner/edit',
        method: 'post',
        data: data
    })
}

//删除转让方
export function delAssetOwner(id) {
    let data = {
        id: id
    }
    return request({
        url: '/caseManage/asset/owner/deleted',
        method: 'post',
        data: data
    })
}

//获取产品列表
export function getProList(query) {
    return request({
        url: '/caseManage/asset/product/list',
        method: 'get',
        params: query
    })
}

//修改产品状态
export function setProdStata(data) {
    return request({
        url: '/caseManage/asset/product/updateState',
        method: 'post',
        data: data
    })
}

//获取转让方列表
export function assetOwnerList() {
    return request({
        url: '/caseManage/asset/owner/list',
        method: 'get'
    })
}

//检查产品类型
export function checkProName(query) {
    return request({
        url: '/caseManage/asset/product/checkName',
        method: 'get',
        params:query
    })
}

//获取模板字段
export function getTplFields(tplid) {
    return request({
        url: '/caseManage/asset/product/getInitTemplate',
        method: 'get',
        params: {currencyTemplateId: tplid}
    })
} 

//创建产品
export function addProduct(data) {
    return request({
        url: '/caseManage/asset/product/add',
        method: 'post',
        data: data
    })
}

//获取产品详情
export function productDetail(id) {
    let data = {
        id: id
    }
    return request({
        url: '/caseManage/asset/product/findProduct',
        method: 'post',
        data: data
    })
}

//编辑产品
export function editProduct(data) {
    return request({
        url: '/caseManage/asset/product/edit',
        method: 'post',
        data: data
    })
}

//删除产品
export function delProduct(id) {
    let data = {
        id: id
    }
    return request({
        url: '/caseManage/asset/product/deleted',
        method: 'post',
        data: data
    })
}

//关键词搜索
export function getDictTransferor(query) {
    return request({
        url: '/caseManage/asset/owner/getDictTransferor',
        method: 'get',
        params:query
    })
}

//关键词搜索
export function getDictProductType(query) {
    return request({
        url: '/caseManage/asset/product/getDictProductType',
        method: 'get',
        params:query
    })
}

//创建方下拉框
export function addName(query) {
    return request({
        url: '/caseManage/asset/owner/add',
        method: 'post',
        params:query
    })
}