import request from '@/utils/request'

//获取发送记录列表
export function selectSendRecords(query) {
  return request({
    url: '/caseManage/management/selectSendRecords',
    method: 'get',
    params: query
  })
}

//获取下拉状态
export function getContacts(query) {
  return request({
    url: '/caseManage/management/contacts',
    method: 'get',
    params: query
  })
}

//查看回复内容
export function getProce(query) {
  return request({
    url: `caseManage/management/selectReplayById/${query.id}`,
    method: 'get',
  })
}

// 获取全部状态的总和
export function selectRecordNumber(query) {
  return request({
    url: `caseManage/management/selectRecordNumber`,
    method: 'get',
    params: query
  })
}

// 导出资产端
export function exportZcPage(data) {
  return request({
    url: `caseManage/management/exportZcPage`,
    method: 'post',
    data: data
  })
}


// 导出催收端
export function exportCsPage(data) {
  return request({
    url: `caseManage/management/exportCsPage`,
    method: 'post',
    data: data
  })
}



