import request from '@/utils/request'

//快速分案-搜索结果
export function jointDebtList(query) {
    return request({
        url: '/caseManage/case/manage/jointDebtList',
        method: 'get',
        params: query
    })
}

//查询共债案件
export function selectManageCaseId(query) {
    return request({
        url: '/caseManage/case/manage/selectManageCaseId',
        method: 'get',
        params: query
    })
}


//案件统计
export function getCaseCount(data) {
    return request({
        url: '/caseManage/case/manage/selectJointDebtCount',
        method: 'post',
        data: data
    })
}

//搜索结果
export function jointDebtAllocationQuery(query) {
    return request({
        url: '/caseManage/case/manage/jointDebtAllocationQuery',
        method: 'get',
        params: query
    })
}

//搜索结果
export function jointDebtAllocation(data) {
    return request({
        url: '/caseManage/case/manage/jointDebtAllocation',
        method: 'post',
        data: data
    })
}