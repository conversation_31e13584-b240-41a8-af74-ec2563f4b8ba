import request from '@/utils/request'

//获取列表
export function messageList(query) {
  return request({
    url: '/system/notice/list',
    method: 'get',
    params: query
  })
}

//发布消息
export function addMessage(data) {
  return request({
    url: '/system/notice',
    method: 'post',
    data: data
  })
}

//获取消息详情
export function getMesgDetail(id) {
  return request({
    url: '/system/notice/'+id,
    method: 'get',
  })
}

//修改消息
export function updatedMesg(data) {
  return request({
    url: '/system/notice',
    method: 'put',
    data: data
  })
}

//删除消息
export function deleteMesg(id) {
  return request({
    url: '/system/notice/'+id,
    method: 'delete'
  })
}