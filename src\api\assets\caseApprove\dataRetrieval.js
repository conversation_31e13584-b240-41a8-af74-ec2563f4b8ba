import request from '@/utils/request'

//获取停催审批列表
export function dataRetrievalList(query) {
    return request({
        url: '/caseManage/approve/retrieval/list',
        method: 'get',
        params: query
    })
}

//获取审批状态下拉 
export function getExamineStates() {
    return request({
        url: '/caseManage/case/start/getExamineStates',
        method: 'get'
    })
}

//获取委案批次号 
export function getEntrustingCaseBatchNums() {
    return request({
        url: '/caseManage/case/start/getEntrustingCaseBatchNums',
        method: 'get'
    })
}

//不通过
export function dataRetrievalnotPass(data) {
    return request({
        url: '/caseManage/approve/retrieval/notPass',
        method: 'post',
        data: data
    })
}

//通过
export function dataRetrievalPass(data) {
    return request({
        url: '/caseManage/approve/retrieval/pass',
        method: 'post',
        data: data
    })
}

//查看案件进度
export function getProce(query) {
    return request({
        url: '/caseManage/approve/retrieval/proce',
        method: 'get',
        params: query
    })
}

//查询勾选信息
export function getTickBoxInfo(query) {
    return request({
        url: '/caseManage/management/retrieval/getTickBoxInfo',
        method: 'get',
        params: query
    })
}