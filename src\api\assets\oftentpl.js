import request from '@/utils/request'

//获取列表
export function getOftenTpl(query) {
    return request({
        url: '/caseManage/asset/currencyTemplates/list',
        method: 'get',
        params: query
    })
}

//获取常用模板options
export function getOftenTplOptions() {
    return request({
        url: '/caseManage/asset/currencyTemplates/getCurrencyTemplates',
        method: 'get',
    })
}

//修改状态
export function updateState(data) {
    return request({
        url: '/caseManage/asset/currencyTemplates/updateState',
        method: 'post',
        data: data
    })
}

//获取模板信息
export function getTplinfo(id) {
    return request({
        url: '/caseManage/asset/currencyTemplates/get',
        method: 'get',
        params: {id: id}
    })
}

//添加模板
export function addOftenTpl(data) {
    return request({
        url: '/caseManage/asset/currencyTemplates/add',
        method: 'post',
        data: data
    })
}

//编辑模板
export function editOftenTpl(data) {
    return request({
        url: '/caseManage/asset/currencyTemplates/edit',
        method: 'post',
        data: data
    })
}