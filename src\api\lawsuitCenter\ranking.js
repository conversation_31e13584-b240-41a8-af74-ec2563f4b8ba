import request from '@/utils/request'

// 诉讼费-根据机构统计排名
export function teamCosts(query) {
  return request({
    url: '/caseManage/caseManage/teamCosts',
    method: 'get',
    params: query
  })
}

// 开庭费-根据机构统计排名
export function teamCourtFees(query) {
  return request({
    url: '/caseManage/caseManage/teamCourtFees',
    method: 'get',
    params: query
  })
}

// 保全费-根据机构统计排名
export function teamPreservationFee(query) {
  return request({
    url: '/caseManage/caseManage/teamPreservationFee',
    method: 'get',
    params: query
  })
}

// 执行还款-根据机构统计排名
export function executeRepayment(query) {
  return request({
    url: '/caseManage/caseManage/executeRepayment',
    method: 'get',
    params: query
  })
}
