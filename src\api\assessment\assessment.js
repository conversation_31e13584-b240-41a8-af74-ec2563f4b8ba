import request from '@/utils/request'

//获取转让方信息
export function selectAppraiser(query) {
    return request({
        url: '/caseManage/appraisal/selectAppraiser',
        method: 'get',
        params: query
    })
}

//获取转让方模板信息
export function selectText(query) {
    return request({
        url: '/caseManage/appraisal/selectText',
        method: 'get',
        params: query
    })
}

//修改转让方模板信息
export function updateText(data) {
    return request({
        url: '/caseManage/appraisal/updateText',
        method: 'post',
        data: data
    })
}

//下载查询模板
export function selectTemplate(query) {
    return request({
        url: '/caseManage/appraisal/selectTemplate',
        method: 'get',
        params: query
    })
}


//下载查询模板
export function verification(query) {
    return request({
        url: '/caseManage/appraisal/verification',
        method: 'post',
        params: query
    })
}

//提交正确数据
export function insertList(data) {
    return request({
        url: '/caseManage/appraisal/insert',
        method: 'post',
        data: data,
        timeout: 7 * 60 * 1000
    })
}

//查询参数设置信息
export function selectParameterSetting(query) {
    return request({
        url: '/caseManage/appraisal/selectParameterSetting',
        method: 'get',
        params: query
    })
}

//查询导入信息
export function selectEvaluationRecord(query) {
    return request({
        url: '/caseManage/appraisal/selectEvaluationRecord',
        method: 'get',
        params: query
    })
}

//查询下载异常导出
export function exportError(query) {
    return request({
        url: '/caseManage/appraisal/exportError',
        method: 'post',
        params: query
    })
}

//查询下载异常导出
export function updateParameterSetting(data) {
    return request({
        url: '/caseManage/appraisal/updateParameterSetting',
        method: 'post',
        data: data
    })
}

//综合数据评估
export function comprehensiveDataEvaluation(query) {
    return request({
        url: '/caseManage/appraisal/comprehensiveDataEvaluation',
        method: 'get',
        params: query
    })
}

//群体影像
export function groupImageAttribute(query) {
    return request({
        url: '/caseManage/appraisal/groupImageAttribute',
        method: 'get',
        params: query
    })
}

//多头借贷属性
export function generateData(query) {
    return request({
        url: '/caseManage/appraisal/generateData',
        method: 'get',
        params: query
    })
}

//地区分布
export function geographicalDistribution(query) {
    return request({
        url: '/caseManage/appraisal/geographicalDistribution',
        method: 'get',
        params: query
    })
}

//年龄分布
export function ageAssessment(query) {
    return request({
        url: '/caseManage/appraisal/ageAssessment',
        method: 'get',
        params: query
    })
}

//性别分布
export function genderAssessment(query) {
    return request({
        url: '/caseManage/appraisal/genderAssessment',
        method: 'get',
        params: query
    })
}

//实际逾期天数分布
export function overdueDays(query) {
    return request({
        url: '/caseManage/appraisal/overdueDays',
        method: 'get',
        params: query
    })
}

//资产规模分布
export function assetScaleAssessment(query) {
    return request({
        url: '/caseManage/appraisal/assetScaleAssessment',
        method: 'get',
        params: query
    })
}

//债权金额评估
export function loanAmountDistribution(query) {
    return request({
        url: '/caseManage/appraisal/loanAmountDistribution',
        method: 'get',
        params: query
    })
}

//账龄评估
export function agingDistribution(query) {
    return request({
        url: '/caseManage/appraisal/agingDistribution',
        method: 'get',
        params: query
    })
}

//贷款期数分布
export function distributionLoanPeriods(query) {
    return request({
        url: '/caseManage/appraisal/distributionLoanPeriods',
        method: 'get',
        params: query
    })
}

//贷款时间分布
export function distributionLendingTime(query) {
    return request({
        url: '/caseManage/appraisal/distributionLendingTime',
        method: 'get',
        params: query
    })
}

//诉讼情况/银行个贷
export function litigationSituation(query) {
    return request({
        url: '/caseManage/appraisal/litigationSituation',
        method: 'get',
        params: query
    })
}

//担保情况/银行个贷
export function guaranteeAssessment(query) {
    return request({
        url: '/caseManage/appraisal/guaranteeAssessment',
        method: 'get',
        params: query
    })
}

//抵押情况/银行个贷
export function mortgageEvaluation(query) {
    return request({
        url: '/caseManage/appraisal/mortgageEvaluation',
        method: 'get',
        params: query
    })
}

//银行卡额度分析/银行个贷
export function creditCardEvaluation(query) {
    return request({
        url: '/caseManage/appraisal/creditCardEvaluation',
        method: 'get',
        params: query
    })
}

//债权证据链完整性评估数据
export function selectCreditEvidence(query) {
    return request({
        url: '/caseManage/appraisal/selectCreditEvidence',
        method: 'get',
        params: query
    })
}

//债权证据链完整性评估数据
export function updateCreditEvidence(data) {
    return request({
        url: '/caseManage/appraisal/updateCreditEvidence',
        method: 'post',
        data: data
    })
}


//债权证据链完整性评估数据
export function deleteData(query) {
    return request({
        url: '/caseManage/appraisal/delete',
        method: 'post',
        params: query
    })
}

//债权证据链完整性评估数据
export function updateAssetOverview(data) {
    return request({
        url: '/caseManage/appraisal/updateAssetOverview',
        method: 'post',
        data: data
    })
}
//学历分布、贷款利率分布
export function otherDistribution(params) {
    return request({
        url: '/caseManage/appraisal/otherDistribution',
        method: 'get',
        params
    })
}


















