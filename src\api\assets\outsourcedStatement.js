import request from '@/utils/request'

//委外对账列表
export function selectOutsourcedList(query) {
    return request({
        url: '/caseManage/outsourced/selectOutsourced',
        method: 'get',
        params: query
    })
}

// 产品下拉框
export function productTypeOption(query) {
    return request({
        url: '/caseManage/outsourced/getDictProductType',
        method: 'get',
        params: query
    })
}

// 获取金额
export function selectAmountCount(query) {
    return request({
        url: '/caseManage/outsourced/selectAmountCount',
        method: 'get',
        params: query
    })
}

// 催收机构--下拉
export function caseFreezeCaseCount(data) {
    return request({
        url: '/caseManage/litigation/caseFreezeCaseCount',
        method: 'post',
        data: data
    })
}