import request from '@/utils/request'

//获取列表
export function checkbillList(query) {
  return request({
    url: '/caseManage/financial/armanage/list',
    method: 'get',
    params: query
  })
}

//导入对账单
export function importBill(data) {
  return request({
    url: '/caseManage/financial/armanage/importBill',
    method: 'post',
    data: data
  })
}

//撤销对账
export function revokeBill(id) {
  return request({
    url: '/caseManage/financial/armanage/revoke',
    method: 'post',
    data: {id: id}
  })
}

//开始自动审核
export function openAutoApproval(id) {
  return request({
    url: '/caseManage/financial/armanage/openAutoApproval',
    method: 'post',
    data: {id: id}
  })
}

//关闭自动审核
export function closeAutoApproval(id) {
  return request({
    url: '/caseManage/financial/armanage/closeAutoApproval',
    method: 'post',
    data: {id: id}
  })
}