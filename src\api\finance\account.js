import request from '@/utils/request'

//获取列表
export function getAccountList(query) {
  return request({
    url: '/caseManage/financial/account/list',
    method: 'get',
    params: query
  })
}

//添加账户
export function addAccount(data) {
    return request({
      url: '/caseManage/financial/account/insert',
      method: 'post',
      data: data
    })
  }

//修改账户
export function updateAccount(data) {
    return request({
      url: '/caseManage/financial/account/update',
      method: 'post',
      data: data
    })
  }

  
//删除账户
export function delAccount(query) {
    return request({
      url: '/caseManage/financial/account/delete',
      method: 'post',
      params: query
    })
  }